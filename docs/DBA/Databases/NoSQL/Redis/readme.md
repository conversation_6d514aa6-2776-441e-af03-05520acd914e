# Redis

Redis is an open-source, in-memory data structure store used to implement NoSQL key-value databases.

- Komut işleme: Tek iş parça<PERSON>ıklı (single-threaded)
- I/O, snapshot ve AOF işlemleri: Çok iş parçacıklı (multi-threaded)

## Basic Data Types

### Strings
Redis tarafında stringleri binary safe olduğundan dolayı value tarafında istediğ<PERSON>iz her şeyi kaydedebiliriz yani value string olmak zorunda değildir. Bir image’da pdf’de kaydedebiliriz

This is the base and simplest data structure in Redis that can be associated with a key. Strings in Redis are binary safe and support polymorphism which means that a string key can be modified to hold binary, integer, or even floating-point values. String in Redis has a limit of 512 Mb data size that can be stored in them.

```
    SET key value
    GET key
    INCR counter
    DECR counter
```

### List
This is an ordered collection of elements. Redis list is similar to Arrays in Ruby or JavaScript but differs in that it is implemented using a linked list. Lists in Redis provides a simple way to implement queues, stacks, and other data structures that rely on ordering.
Liste şeklinde veriyi tutabiliyoruz. <PERSON><PERSON><PERSON> yan<PERSON> ba<PERSON><PERSON> ekle, sondan ekle ya da baştan sil, sondan sil gibi özelliklerimiz bulunmaktadır. Araya bir data ekleyemiyoruz ya başına ekliyoruz ya da sonuna ekliyoruz.

```
LPUSH mylist value  // Listeye soldan veri  ekleme
RPUSH mylist value  // Listeye sağdan veri  ekleme
LRANGE mylist 0 -1  // Bir aralık kullanarak listenin elemanlarını almak için kullanılır.
LPOP mylist         // Dizinin solundan sil
RPOP mylist         // Dizin sağından sil
LINDEX mylist 1     // Dizinin belli bir index’deki elemanı getirme
LLEN mylist         // Listenin uzunluğu
```
### Sets
List’den 2 farklı özelliği vardır. Dizin içerisinde tutacağımız veriler benzersiz olmalıdır yani aynı veriden 2 tane bulunamaz. List’lerde olan başa sona ekleme özelliği Set’de yok. Sette eklenen veri random olarak dizi içerisinde herhangi bir yere eklenir ve set’ler içerisinde de verilerimiz binary olarak kaydedileceğinden ötürü bir image, pdf gibi dosya türlerini kaydedebiliriz.

```
SADD myset value        // add an item
SMEMBERS myset          //return the items
SREM myset value        //remove an item
SISMEMBER myset value   // Check if an item is found
```
### Hash
Verileri key ve value olarak tutabileceğimiz, c# tarafındaki dictionary’ye benzeyen bir veri tipidir.

```
HSET user:1 name "John"   // set a hash key-value pair.
HGET user:1 name          // get a value using a key
HGETALL user:1            // return all key-value pairs.
HDEL user:1 name        // delete key
```
### Sorted Sets (Zset)
A zset (sorted-set) is a data type unique to Redis. It is similar to the set data type because it contains a collection of unique items. It is also similar to a hash because each item is assigned to a score (float numbers), just like a hash's key is assigned to a value. The big idea of zset is to make it easy to sort items based on their assigned scores (hence the name sorted-set)

```
ZADD hellozset 500 first
ZRANGE hellozset 0 -1
ZRANGE hellozset 0 -1 WITHSCORES
ZRANGEBYSCORE hellozset 0 450
ZREM hellozset first

```

## Redis CLI

```
# Bağlantı
redis-cli
redis-cli -h host -p port -a password

# Monitoring
MONITOR
INFO
```

## Caching Stratejileri

- Cache-aside
- Write-through
- Write-behind
- Refresh-ahead
- Cache eviction policies
## Persistence
### RDB
### AOF
## Pub/Sub

```
# Publisher
PUBLISH channel message

# Subscriber
SUBSCRIBE channel
```

## Disturubeted System Architectures

### 1. Redis Sentinel

Redis Sentinel, Redis veritabanının yüksek kullanılabilirlik (High Availability) sağlamak amacıyla geliştirilmiş bir yönetim servisidir. Redis'in doğal failover mekanizması olarak çalışan Sentinel, Master-Slave replikasyonu ile entegre bir şekilde çalışır. Bir Master sunucusunun erişilemez hale gelmesi durumunda, otomatik olarak bir Slave sunucusunu Master olarak atar ve sistemin kesintisiz çalışmasını sağlar.

Redis Sentinel, Redis sunucularını sürekli olarak izler ve sunucu hataları durumunda otomatik olarak devreye girer. Redis'in master-slave replikasyonunu kullanarak yüksek erişilebilirlik sağlar. Eğer master sunucusu çökerse, Sentinel en uygun slave sunucusunu master olarak atar ve sistemin sürekli çalışmasını garanti eder.

#### Redis Sentinel Mimarisi
Redis Sentinel, master ve slave sunucuları ile birlikte çalışır:

- Master: Yazma ve okuma işlemlerinin gerçekleştiği birincil sunucudur. Sentinel bu sunucuyu sürekli izler ve bir sorun oluştuğunda slave sunuculardan birini yeni master olarak atar. Sentinel her zaman en güncel master sunucuyu takip eder.

- Slave: Master sunucudan verileri replike eden yedek sunucudur. Okuma işlemlerini gerçekleştirir ve bir Sentinel kurulumunda birden fazla slave bulunabilir.

#### Redis Sentinel'in Temel Bileşenleri
- İzleme (Monitoring): Sentinel, Redis sunucularını sürekli olarak izler ve bir sunucuya erişilemez hale geldiğinde onu "down" olarak kabul eder.

- Bildirim (Notification): Bir sorun tespit edildiğinde, Sentinel sistem yöneticilerini bilgilendirir ve gerekirse diğer Sentinel'leri de haberdar eder.

- Failover: Eğer master sunucusu çökerse, Sentinel otomatik olarak bir slave sunucusunu master olarak atar ve diğer tüm slave sunucuların yeni master'a bağlanmasını sağlar.

#### Redis Sentinel Çalışma Mantığı

Sentinel bir durum makinesi (state machine) olarak çalışır ve belirli aralıklarla (varsayılan olarak 100ms) çalışarak kümenin mevcut durumunu değerlendirir.

1. Sağlıklı Durum (Healthy)

    - Sentinel, master sunucuyu PING mesajlarıyla kontrol eder. Eğer geçerli yanıt alıyorsa, herhangi bir işlem yapmaz.

2. Öznel Çöküş (SDOWN - Subjectively Down)

    - Eğer Sentinel, belirlenen süre boyunca master’dan geçerli bir PING yanıtı alamazsa, onu öznel olarak çökmüş (SDOWN) kabul eder.
    - Ancak, yalnızca kendi değerlendirmesiyle karar vermez. Diğer Sentinellerin de aynı görüşte olup olmadığını anlamak için SENTINEL-IS-MASTER-DOWN-BY-ADDR isteğini diğer Sentinellere gönderir.

3. Nesnel Çöküş (ODOWN - Objectively Down)

    - Diğer Sentinellerin çoğunluğu (quorum) da master’ın çöktüğünü kabul ederse, master nesnel olarak çökmüş (ODOWN) olarak işaretlenir.
    - Eğer henüz başka bir Sentinel failover başlatmadıysa, seçim süreci başlar.

4. Seçim Süreci (Election)

    - Bir Sentinel’in yeni master belirleyebilmesi için, diğer Sentinellerin oylarını alarak lider olarak seçilmesi gerekir.
    - Seçimi kazanan Sentinel failover sürecini yönetir, kaybederse hiçbir işlem yapmaz.

5. Failover Süreci

    - Sentinel, en uygun replika sunucusunu yeni master olarak seçer.
    - Seçilen replika, master olarak atanır (replicaof no one komutuyla).
    - Diğer replikalar, yeni master’ı takip edecek şekilde yeniden yapılandırılır.
    - Eğer Sentinel uygun bir replika bulamaz veya süreç başarısız olursa, failover iptal edilir.

6. Yapılandırma Yönetimi

    - Sentinel, yapılandırmalarını sürekli olarak günceller ve disk üzerine kaydeder.
    - Bir Sentinel yeniden başlatıldığında, son yapılandırmayı kullanarak kaldığı yerden devam eder.
    - Her Sentinel, izlemekle sorumlu olduğu Redis master’larını konfigüre eder.
    - Sentinel, INFO komutları ile sistemdeki tüm replikaları ve diğer Sentinelleri keşfeder.
    - Yapılandırma değişiklikleri (failover, yeni Sentinellerin eklenmesi vb.) disk üzerine kaydedilir.
    - Sentinel her başlatıldığında, önceki çalışmada kaydedilen yapılandırma dosyasından devam eder.
    - Sentinel, belirli aralıklarla (INFO komutu ile) tüm Redis sunucularından bilgi toplar.
    - Yeni replikalar keşfedildiğinde, izleme listesine eklenir.

7. Gossip Protokolü ile İletişim

    - Sentineller, Redis pub/sub mekanizmasını kullanarak sentinel:hello kanalında birbirlerine mesaj gönderirler.
    - Her Sentinel düzenli olarak bu kanala kendi IP, port ve kimlik bilgisini içeren bir hello mesajı gönderir.
    - Bu mesajlar sayesinde, Sentineller yeni katılan veya değişen Sentinel düğümlerini keşfedebilir.

8. Epoch ve Konfigürasyon Güncellemeleri

    - Her Sentinel, belirli bir epoch numarasıyla konfigürasyon güncellemelerini takip eder.
    - Eğer daha yeni bir epoch’a sahip bir güncelleme alırsa, eski veriyi reddeder ve yeni konfigürasyonu uygular.
    - Güncellemeler yalnızca mevcut epoch değerinden daha yüksekse kabul edilir.
    - Failover başlatıldığında, ilgili Sentinel kendi epoch değerini artırarak lider seçimi için seçim başlatır.

![sentinel_how_works](./assets/sentinel_work_diagram.png)

> #### Quorum ve Karar Mekanizması
> Quorum, bir sistemde bir kararın alınabilmesi için gerekli olan minimum onay sayısını ifade eder. Dağıtık sistemlerde, özellikle High Availability (HA) senaryolarında ve failover mekanizmalarında kullanılır.

> #### Quorum Gereksinimi
> Redis Sentinel, bir Master node'un erişilemez olduğunu doğrulamak için quorum gerektirir. Quorum, Sentinel node'larının çoğunluğunun (örneğin: 2 Sentinel'den en az 2'si) Master'ın down olduğunu kabul etmesi anlamına gelir. Tek bir Sentinel kullanıldığında quorum sağlanamaz, bu nedenle otomatik failover gerçekleşmez.

> #### Split-Brain Senaryolarını Önlemek 
> Split-brain, ağ bölünmesi (network partition) nedeniyle iki farklı Sentinel grubunun birbirini görememesi durumudur. 3 Sentinel, quorum'u koruyarak bir tarafın doğru karar vermesini sağlar.

> #### Yüksek Erişilebilirlik
> 3 Sentinel, failover süreçlerinde daha güvenilir kararlar verir. Eğer bir Sentinel node down olursa, hala quorum sağlanabilir ve failover işlemleri devam edebilir.

#### Redis Sentinel'in Avantajları
- Otomatik Failover: Master sunucusu çöktüğünde, Sentinel otomatik olarak failover işlemini gerçekleştirir ve sistem yöneticilerinin müdahalesine gerek kalmaz.

- Yüksek Erişilebilirlik: Redis sisteminin kesintisiz çalışmasını sağlayarak yüksek erişilebilirlik sunar.

- Esnek Yapı: Dağıtık bir yapı ile sistem güvenilirliğini artırır ve birden fazla Sentinel sunucusunun birlikte çalışmasına olanak tanır.

#### Redis Sentinel Ne Zaman Kullanılır?
Redis Sunucusu Hatası Durumunda: Eğer bir Redis sunucusu çökerse veya kesinti yaşanırsa, Sentinel farklı bir Redis sunucusuna geçiş yaparak hizmetin devam etmesini sağlar.

Bakım ve Güncelleme Süreçlerinde: Redis sunucusu bakım veya güncelleme nedeniyle geçici olarak kullanılamaz hale gelirse, Sentinel farklı bir sunucu kullanarak hizmet sürekliliğini korur.

Yüksek Trafik Durumlarında: Sentinel, Redis sunucularını ölçeklendirerek performansı artırır ve yüksek trafik uygulamalarında yükü dengeli bir şekilde dağıtarak hız ve stabilite sağlar.

#### Redis Sentinel Example

![sentinel_how_works](./assets/sentinal_master_slave_diagram.png)

![sentinel_how_works](./assets/sentinel_monitoring.png)

### 2. Redis Cluster

Daha büyük ve karmaşık veri kümeleri için horizontal scalability sunar. Verileri shard’lara bölerek hem performansı artırır hem de failover durumlarında sorunsuz bir geçiş sağlar.


Redis Cluster is designed for replicating and sharding distributed data, allowing data to be spread across multiple Redis nodes.

Key Features:

Horizontal Scaling: Scales Redis horizontally by distributing data across multiple nodes.
Automatic Data Sharding: Automatically directs data to the appropriate node.
High Availability: Ensures high availability by selecting a new node if the main node fails and supports data replication and fault tolerance.
Data Resharding: Allows dynamic addition and removal of nodes without data loss.
No Client-Side Redirects: Ensures requests go directly to the appropriate node without redirection.
No Single Point of Failure: The system continues operating even if one node fails.