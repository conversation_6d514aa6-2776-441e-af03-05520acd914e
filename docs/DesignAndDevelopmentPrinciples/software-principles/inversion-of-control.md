# Inversion of Control (IoC)

Inversion of Control (IoC) is a design principle used in software development, particularly significant in object-oriented programming. This concept aims to transfer control of the software from the developed code to a framework or container.

## Core Principle of IoC
In traditional programming, your code controls the flow and creates the objects it needs. IoC reverses this approach: instead of your program being called, it's called and controlled by a framework or container.
The main slogan is: "Don't call us, we'll call you."
## Advantages of IoC

- Modularity: Components can be developed and tested independently.
- Loose Coupling: Dependencies between components are reduced.
- Testability: Testing with mock objects becomes easier.
- Parallel Development: Teams can work independently.
- Lifecycle Management: The framework can manage object lifecycles.
- Focus: Developers can focus on business logic.

## IoC Implementation Techniques

1. Dependency Injection (DI)
The most common IoC implementation technique. Dependencies are provided externally:

This is the most widely used type of IoC. Dependencies are provided to classes from outside via constructor, setter or interface. Thus, the class does not need to resolve its dependencies itself.

- Constructor Injection: Dependencies are given as parameters to the constructor method of the class.
- Setter Injection: Dependencies are injected into the class via setter methods.
- Interface Injection: Dependencies are provided through the implementation of an interface of the class.


2. Service Locator
Obtains dependencies through a central service locator:

```java

class CustomerService {
    private CustomerRepository repository;
    
    public CustomerService() {
        this.repository = ServiceLocator.getService(CustomerRepository.class);
    }
}

```

3. Event-based IoC
Control flow based on events:

```java

// Traditional
void processData() {
    readData();
    processData();
    saveData();
}

// Event-based
void initialize() {
    eventBus.subscribe("dataReady", this::processData);
    eventBus.subscribe("dataProcessed", this::saveData);
}

```

4. Template Method Pattern
Defines the skeleton of an algorithm in a class, leaving some steps to subclasses:

```java
abstract class DataProcessor {
    // Framework code - control is here
    public final void process() {
        openFile();
        readData();
        processData(); // To be implemented by subclass
        closeFile();
    }
    
    protected abstract void processData();
    
    private void openFile() { /*...*/ }
    private void readData() { /*...*/ }
    private void closeFile() { /*...*/ }
}
```

## IoC and Dependency Management
The core idea of IoC is to delegate the management of dependencies to an external source. A class does not create the dependencies it needs; instead, they are provided from the outside. This increases the flexibility of the software because:

- Dependencies can be provided externally: Modules or components access the resources they need from external sources.
- Testability improves: Since dependencies are provided externally, mock objects (fake objects) can be used during testing instead of real instances.
- Flexibility and reusability increase: Modules and classes become independent of each other, making them more reusable and adaptable.



## Dependency injection

Dependency Injection (DI) is a design pattern used to implement Inversion of Control (IoC). This pattern ensures that a class does not create its own dependencies but instead receives them from an external source. A DI Container or IoC Container manages these dependencies and provides them when needed.

### Advantages of Dependency Injection
- Simplifies Dependency Management: Objects do not create their dependencies, allowing a central place to manage them.
- Improves Testability: Instead of real dependencies, mock or stub objects can be used in testing.
- Ensures Loose Coupling: Objects are not tightly coupled, making changes easier with minimal side effects.

### Types of Dependency Injection
Dependency Injection can be implemented in three different ways:

1. Constructor Injection

Dependencies are provided through the constructor of the class.
It is the most commonly used DI method.
Ensures that all required dependencies are available at object creation, reducing runtime errors due to missing dependencies.

```csharp

// Step 1: Define an Interface
public interface IMessageService
{
    void SendMessage(string message);
}

// Step 2: Implement the Interface
public class EmailService : IMessageService
{
    public void SendMessage(string message)
    {
        Console.WriteLine($"Email sent: {message}");
    }
}

// Step 3: Inject Dependency via Constructor
public class NotificationService
{
    private readonly IMessageService _messageService;

    public NotificationService(IMessageService messageService)
    {
        _messageService = messageService;
    }

    public void Notify(string message)
    {
        _messageService.SendMessage(message);
    }
}

// Step 4: Usage
class Program
{
    static void Main()
    {
        IMessageService emailService = new EmailService();
        NotificationService notification = new NotificationService(emailService);
        notification.Notify("Hello, Dependency Injection!");
    }
}


```

2. Setter Injection

Dependencies are provided through public setter methods of the class.
Offers flexibility as dependencies can be changed after object creation.
However, it can introduce risks if required dependencies are not set before use.

```csharp

public class NotificationService
{
    private IMessageService _messageService;

    // Dependency injected via a public property
    public void SetMessageService(IMessageService messageService)
    {
        _messageService = messageService;
    }

    public void Notify(string message)
    {
        _messageService?.SendMessage(message);
    }
}

class Program
{
    static void Main()
    {
        IMessageService emailService = new EmailService();
        NotificationService notification = new NotificationService();
        notification.SetMessageService(emailService);
        notification.Notify("Hello with Setter Injection!");
    }
}


```

3. Method (Interface) Injection

Dependencies are provided via a method call at runtime.
Often used when injecting dependencies into multiple objects.
Less commonly used in DI frameworks compared to constructor injection.
DI works alongside the Dependency Inversion Principle, making software more modular, flexible, and testable.

```csharp

public class NotificationService
{
    public void Notify(string message, IMessageService messageService)
    {
        messageService.SendMessage(message);
    }
}

class Program
{
    static void Main()
    {
        IMessageService emailService = new EmailService();
        NotificationService notification = new NotificationService();
        notification.Notify("Hello with Method Injection!", emailService);
    }
}

```
