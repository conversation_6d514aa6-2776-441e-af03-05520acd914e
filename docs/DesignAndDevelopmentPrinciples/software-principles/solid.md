# SOLID

## S: Single Responsibility Principle

A class should have only one responsibility. If a class has multiple responsibilities, they become tightly coupled, and a change in one responsibility may affect the other. Therefore, a class should have only one reason to change.

<table>
<tr>
<td>

```csharp

class User
{
    public void SaveToDatabase()
    {
        Console.WriteLine("Veritabanına kaydedildi.");
    }

    public void SendEmail()
    {
        Console.WriteLine("Kullanıcıya e-posta gönderildi.");
    }
}


```
</td>
<td>

```csharp

class UserRepository
{
    public void Save(User user)
    {
        Console.WriteLine("Veritabanına kaydedildi.");
    }
}

class EmailService
{
    public void SendEmail(User user)
    {
        Console.WriteLine("Kullanıcıya e-posta gönderildi.");
    }
}

```

</td>
</tr>
</table>

## O: Open-Closed Principle

Software entities (classes, modules, functions) should be open for extension but closed for modification. New behaviors should be added by extending existing structures rather than modifying their source code. This enhances flexibility and ensures that existing functionality remains intact.

<table>
<tr>
<td>

```csharp
class Rectangle
{
    public double Width { get; set; }
    public double Height { get; set; }
}

class Circle
{
    public double Radius { get; set; }
}

class AreaCalculator
{
    public double CalculateArea(object shape)
    {
        if (shape is Rectangle rectangle)
        {
            return rectangle.Width * rectangle.Height;
        }
        else if (shape is Circle circle)
        {
            return Math.PI * circle.Radius * circle.Radius;
        }

        throw new ArgumentException("Unknown shape");
    }
}


```
</td>
<td>

```csharp

// Açık bir arayüz tanımlıyoruz (Open for Extension)
interface IShape
{
    double CalculateArea();
}

// Kapalı bir temel kod yapıyoruz (Closed for Modification)
class Rectangle : IShape
{
    public double Width { get; set; }
    public double Height { get; set; }

    public double CalculateArea() => Width * Height;
}

class Circle : IShape
{
    public double Radius { get; set; }

    public double CalculateArea() => Math.PI * Radius * Radius;
}

// Yeni şekiller eklemek için mevcut kodu değiştirmeye gerek kalmaz
class Triangle : IShape
{
    public double Base { get; set; }
    public double Height { get; set; }

    public double CalculateArea() => 0.5 * Base * Height;
}

class AreaCalculator
{
    public double CalculateTotalArea(List<IShape> shapes)
    {
        double totalArea = 0;
        foreach (var shape in shapes)
        {
            totalArea += shape.CalculateArea();
        }
        return totalArea;
    }
}

```

</td>
</tr>
</table>



## L: Liskov Substitution Principle

A subclass must be able to fully utilize all the properties of the superclass and must not break the correctness of the program when objects of the subclass are replaced by objects of the superclass. Subclasses and superclasses should exhibit the same behavior and should not contain unused or obsolete properties. That is, when objects in a program are replaced by derived classes, the functioning of the program should continue unchanged.

<table>
<tr>
<td>

```csharp
class Rectangle
{
    public virtual int Width { get; set; }
    public virtual int Height { get; set; }

    public int GetArea()
    {
        return Width * Height;
    }
}

class Square : Rectangle
{
    public override int Width
    {
        set { base.Width = base.Height = value; } // Genişliği değiştirdiğimizde yüksekliği de değiştiriyoruz.
    }

    public override int Height
    {
        set { base.Width = base.Height = value; } // Yüksekliği değiştirdiğimizde genişliği de değiştiriyoruz.
    }
}

```
</td>
<td>

```csharp

// LSP'ye uygun olacak şekilde temel bir arayüz tanımlıyoruz
interface IShape
{
    int GetArea();
}

// Rectangle artık IShape'den türetiliyor
class Rectangle : IShape
{
    public int Width { get; set; }
    public int Height { get; set; }

    public int GetArea()
    {
        return Width * Height;
    }
}

// Square artık bağımsız olarak kendi davranışını tanımlıyor
class Square : IShape
{
    public int SideLength { get; set; }

    public int GetArea()
    {
        return SideLength * SideLength;
    }
}

```

</td>
</tr>
</table>



## I: Interface Segregation Principle

An interface should not contain more capabilities than necessary, and objects should only depend on interfaces that include the methods they actually use. Instead of creating large, general-purpose interfaces, smaller, client-specific interfaces should be designed.

🔹 "Many client-specific interfaces are better than one general-purpose interface."
🔹 "Clients should not be forced to depend upon interfaces that they do not use."

According to this principle, an object should not be forced to implement methods it does not need. This makes the code more flexible, manageable, and resilient to changes.


<table>
<tr>
<td>

```csharp

interface IPrinter
{
    void Print(string document);
    void Fax(string document);
}

class Printer : IPrinter
{
    public void Print(string document)
    {
        Console.WriteLine("Printing document: " + document);
    }

    public void Fax(string document)
    {
        Console.WriteLine("Faxing document: " + document);
    }
}

class SimplePrinter : IPrinter
{
    public void Print(string document)
    {
        Console.WriteLine("Printing document: " + document);
    }

    public void Fax(string document)  // Faks özelliği gereksiz
    {
        throw new NotImplementedException("Fax feature is not supported");
    }
}

```
</td>
<td>

```csharp

// Ayrı arayüzler oluşturuluyor
interface IPrinter
{
    void Print(string document);
}

interface IFax
{
    void Fax(string document);
}

class Printer : IPrinter, IFax
{
    public void Print(string document)
    {
        Console.WriteLine("Printing document: " + document);
    }

    public void Fax(string document)
    {
        Console.WriteLine("Faxing document: " + document);
    }
}

class SimplePrinter : IPrinter
{
    public void Print(string document)
    {
        Console.WriteLine("Printing document: " + document);
    }
}

```

</td>
</tr>
</table>


## D: Dependency Inversion Principle

High-level modules should not depend on low-level modules. Both should depend on abstractions.
Abstractions should not depend on details. Details should depend on abstractions.
According to this principle, abstractions (interfaces or abstract classes that define the functionality between modules) should be independent of concrete details (low-level classes or modules). This ensures that the software remains modular, flexible, and testable.

🔹 "Dependency should be on abstractions, not concretions."
🔹 "High-level modules should not depend upon low-level modules. Both should depend upon abstractions."
🔹 "Abstractions should not depend on details. Details should depend on abstractions."

This principle is used to decouple high-level and low-level dependencies, making the software more flexible and maintainable.

<table>
<tr>
<td>

```csharp

class LowLevelModule
{
    public void PerformAction()
    {
        Console.WriteLine("Performing low-level task.");
    }
}

class HighLevelModule
{
    private LowLevelModule _lowLevelModule;

    public HighLevelModule()
    {
        _lowLevelModule = new LowLevelModule();  // High-level module depends on a concrete low-level module
    }

    public void Execute()
    {
        _lowLevelModule.PerformAction();
    }
}


```
</td>
<td>

```csharp

// Using abstractions
interface ITask
{
    void PerformAction();
}

class LowLevelModule : ITask
{
    public void PerformAction()
    {
        Console.WriteLine("Performing low-level task.");
    }
}

class HighLevelModule
{
    private ITask _task;

    public HighLevelModule(ITask task)  // Dependency is on an interface
    {
        _task = task;
    }

    public void Execute()
    {
        _task.PerformAction();
    }
}

class Program
{
    static void Main()
    {
        ITask task = new LowLevelModule();
        HighLevelModule highLevelModule = new HighLevelModule(task);
        highLevelModule.Execute();
    }
}


```

</td>
</tr>
</table>




```C#

class Restaurant
    {
        public string Name { get; set; }

        private Oven Oven = new Oven();

        public Restaurant(string name)
        {
            this.Name = name;
        }

        public void Cook(string item)
        {
            this.Oven.LightGas();
            this.Oven.Bake(item);
            this.Oven.ExtinguishGas();
        }
    }

    class Oven
    {
        public bool On { get; set; }

        public void LightGas()
        {
            Console.WriteLine("Lighting the oven's gas!");
            this.On = true;
        }

        public void ExtinguishGas()
        {
            Console.WriteLine("Extinguishing the oven's gas!");
            this.On = false;
        }

        public void Bake(string item)
        {
            if (!this.On)
            {
                Console.WriteLine("Oven's gas is not turned on.");
            }
            else
            {
                Console.WriteLine("Now baking " + item + "!");
            }
        }
    }


class Restaurant
    {
        public string Name { get; set; }

        public ICooker Cooker { get; set; }

        public Restaurant(string name, ICooker cooker)
        {
            this.Name = name;
            this.Cooker = cooker;
        }
        
        public void Cook(string item)
        {
            this.Cooker.TurnOn();
            this.Cooker.Cook(item);
            this.Cooker.TurnOff();
        }
    }

    interface ICooker
    {
        bool On { get; set; }

        void TurnOn();

        void TurnOff();

        void Cook(string item);
    }

    class Oven : ICooker
    {
        public bool On { get; set; }

        public void TurnOn()
        {
            Console.WriteLine("Turning on the oven!");
            this.On = true;
        }

        public void TurnOff()
        {
            Console.WriteLine("Turning off the oven!");
            this.On = false;
        }

        public void Cook(string item)
        {
            if (!this.On)
            {
                Console.WriteLine("Oven not turned on.");
            }
            else
            {
                Console.WriteLine("Now baking " + item + "!");
            }
        }
    }

    class Stove : ICooker
    {
        public bool On { get; set; }

        public void TurnOn()
        {
            Console.WriteLine("Turning on the stove!");
            this.On = true;
        }

        public void TurnOff()
        {
            Console.WriteLine("Turning off the stove!");
            this.On = false;
        }

        public void Cook(string item)
        {
            if (!this.On)
            {
                Console.WriteLine("Stove not turned on.");
            }
            else
            {
                Console.WriteLine("Now frying " + item + "!");
            }
        }
    }
```