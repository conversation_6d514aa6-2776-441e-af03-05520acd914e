# Cache-Aside Pattern

The Cache-Aside pattern is one of several data caching strategies for your application.

In this strategy, the application acts as mediator between the cache and the database:

![cache-aside-pattern](./assets/cache-aside-pattern.webp)

1. The application tries to load the data from the cache by the key. If the key is present (Cache-Hit) in the cache, the data is returned to the caller.
2. If the data is not in the cache (Cache Miss), the application loads it from the database.
3. The application stores the data in the cache and then returns it to the caller.