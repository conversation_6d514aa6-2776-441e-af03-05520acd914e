# Kafka

In Kafka, a topic can have many partitions, hence in this way you can increase parallelism of processing messages. This enables us to scale our consumer applications to more than one instance.

Apache Kafka isn’t an implementation of a message broker. Instead, it’s a distributed streaming platform.

Unlike RabbitMQ, which is based on queues and exchanges, Kafka’s storage layer is implemented using a partitioned transaction log. Kafka also provides a Streams API to process streams in real time and a Connectors API for easy integration with various data sources


Apache Kafka, büyük veri akışı ve olay kaydı işlemleri için kullanılan bir mesajlaşma platformudur. Verileri yüksek hacimlerde depolama, dağıtma ve işleme kabiliyetine sahiptir. Kafka’nın öne çıkan yönü, verileri uzun süre saklayabilme ve geçmişe dönük verileri okuyabilme yeteneğidir. Özellikle mikro hizmet mimarilerinde ve büyük veri projelerinde sıklıkla tercih edilir.

### Kafka’nın Temel Özellikleri:

- Log tabanlı yapı: <PERSON><PERSON><PERSON>, gelen verileri bir log olarak kaydeder ve bu log üzerinden geçmiş verilere erişim sağlar.
- Yüksek hacimli veri işleme: Kafka, büyük veri setlerini işlemek ve taşımak için optimize edilmiştir.
- Yatay ölçeklenebilirlik: Kafka, büyük veri sistemlerinde ölçeklenebilirliği en iyi sağlayan çözümlerden biridir.

![kafka-work](./assets/kafka-work.png)



# Apache Kafka Detaylı Teknik Dokümantasyonu

## 1. Apache Kafka Nedir?

Apache Kafka, dağıtık bir veri akış platformudur. LinkedIn tarafından geliştirilmiş ve sonrasında Apache Software Foundation'a devredilmiştir. Yüksek performanslı, gerçek zamanlı veri akışı sağlayan, ölçeklenebilir bir mesajlaşma sistemidir.

Temel özellikleri:
- Yüksek throughput (veri işleme kapasitesi)
- Düşük gecikme süresi (latency)
- Yatay ölçeklenebilirlik
- Fault-tolerance (hata toleransı)
- Veri kalıcılığı (persistence)

## 2. Kafka'nın Temel Bileşenleri

### 2.1 Producer (Üretici)
- Kafka'ya veri gönderen uygulamalar/sistemler
- Verileri belirli bir topic'e gönderir
- Belirli bir partition'a veri gönderebilir veya Kafka'nın otomatik dağıtmasına izin verebilir

### 2.2 Consumer (Tüketici)
- Kafka'dan veri okuyan uygulamalar/sistemler
- Consumer groups halinde çalışır
- Offset yönetimi ile hangi mesajların okunduğunu takip eder

### 2.3 Topic (Konu)
- Verilerin kategorize edildiği mantıksal birimler
- Bir veya birden fazla partition'dan oluşur
- Belirli bir süre boyunca veriler saklanabilir

### 2.4 Partition (Bölüm)
- Topic'lerin fiziksel bölümleri
- Sıralı, değişmez log yapısı
- Her partition tek bir broker üzerinde leader olarak bulunur
- Replikasyon için diğer broker'larda follower kopyaları bulunur

### 2.5 Broker
- Kafka sunucuları
- Partition'ları yönetir
- Consumer ve Producer isteklerini karşılar
- Cluster içinde koordineli çalışır

### 2.6 ZooKeeper
- Cluster yönetimi
- Broker'ların durumunu takip eder
- Leader seçimi ve configuration yönetimi
- (Not: Yeni Kafka versiyonlarında ZooKeeper bağımlılığı kaldırılmaktadır)

## 3. Kafka Mimarisi ve Çalışma Prensibi

### 3.1 Veri Yazma (Write) İşlemi
1. Producer bir mesajı belirli bir topic'e gönderir
2. Mesaj, topic'in partition'larından birine yazılır
3. Partition seçimi:
   - Özel bir partition belirtilmişse ona
   - Key varsa, key'in hash'ine göre
   - Round-robin yöntemiyle

### 3.2 Veri Okuma (Read) İşlemi
1. Consumer'lar consumer group'lar halinde çalışır
2. Her partition yalnızca bir consumer tarafından okunur (aynı group içinde)
3. Consumer'lar offset'leri takip ederek hangi mesajları okuduklarını bilir
4. Paralel okuma için partition sayısı kadar consumer çalışabilir

### 3.3 Replikasyon Mekanizması
- Her partition'ın bir leader ve birden fazla follower'ı vardır
- Yazma işlemleri leader üzerinden yapılır
- Follower'lar leader'dan veri kopyalar
- Leader düşerse follower'lardan biri leader olur

### 3.4 Veri Kalıcılığı ve Güvenilirlik
- Veriler disk üzerinde saklanır
- Replikasyon faktörü ile veri güvenliği sağlanır
- Acknowledgment mekanizması ile veri yazma garantisi
- Commit log yapısı ile veri tutarlılığı

## 4. Kafka'nın Yaygın Kullanım Alanları

1. Log Toplama
   - Uygulama logları
   - Sistem metrikleri
   - Audit logları

2. Event Streaming
   - Gerçek zamanlı analitik
   - IoT veri akışı
   - Kullanıcı aktivite takibi

3. Messaging
   - Servisler arası iletişim
   - Asenkron işlem kuyruğu
   - Mikroservis mimarisi

## 5. Performans ve Ölçeklendirme

### 5.1 Performans Faktörleri
- Partition sayısı
- Replikasyon faktörü
- Batch size
- Compression
- Retention period

### 5.2 Ölçeklendirme Stratejileri
- Broker ekleme
- Partition sayısını artırma
- Consumer group yapılandırması
- Hardware optimizasyonu

## 6. Best Practices

1. Partition Yönetimi
   - Topic başına optimal partition sayısı belirleme
   - Partition sayısını cluster kaynaklarına göre ayarlama

2. Monitoring
   - Broker metrics
   - Consumer lag
   - Disk kullanımı
   - Network I/O

3. Backup ve Disaster Recovery
   - Düzenli yedekleme
   - Multi-datacenter replikasyonu
   - Failover planlaması

4. Güvenlik
   - SSL/TLS encryption
   - SASL authentication
   - ACL ile yetkilendirme

