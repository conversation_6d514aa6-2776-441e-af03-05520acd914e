# RabbitMQ

## <PERSON><PERSON><PERSON> ve Temel Tanım

RabbitMQ, mesaj tabanlı iletişimi düzenlemek için kullanılan açık kaynaklı bir mesaj aracısı (message broker) yazılımıdır. <PERSON><PERSON> göre<PERSON>, farklı uygulamalar arasında güvenilir ve esnek mesaj iletişimi sağlamaktır.

RabbitMQ, AMQP(Advanced Message Queuing Protocol) adı verilen bir mesaj protokülü kullanır. Bu protokol, mesajların verimli bir şekilde yönlendirilmesi ve işlenmesi için tasarlanmıştır.

### RabbitMQ’nun Temel Özellikleri:

- Doğrudan mesaj yönlendirme: Mesajları kuyruğa alarak hedef alıcılara iletir.

- Düşük gecikme süresi: Verilerin hızlı bir şekilde iletilmesi gereken sistemlerde düşük gecikme süreleri sunar.

- <PERSON><PERSON><PERSON><PERSON>/Abone (Pub/Sub) modeli: <PERSON>en fazla aboneye aynı mesajı dağıtmak için kullanılır.

- Asenkron İletim: Mesajlar kuyrukta saklanır ve tüketici (consumer) hazır olduğunda işlenir.

- Çoklu Kuyruk Desteği: Farklı işlevlere ayrılmış birden fazla kuyruk oluşturulabilir.

- Yüksek Güvenlik ve Erişilebilirlik: Durable (dayanıklı) ve quorum kuyruğu gibi özellikleri sayesinde veri kaybını önler.

- Ölçeklenebilirlik ve Esneklik: Kullanıcı sayısı arttığında veya işlem hacmi büyüdüğünde yük dengelenebilir.

- Farklı Protokol Desteği: RabbitMQ yalnızca AMQP değil, MQTT ve STOMP gibi protokolleri de destekler.

## Mimari Avantajları

- Sistemleri Ayırma (Decoupling): Farklı sistem bileşenlerinin birbirinden bağımsız çalışmasını sağlama
- Ölçeklenebilirlik: Yüksek eş zamanlı işlem yönetimi
- Trafik Dengeleme: Ani yük artışlarında sistemin performansını koruma
- Güvenilir Mesaj İletimi: Mesajların kaybolmasını önleme

## RabbitMQ ve Bileşenleri

![rabbitmq-components](./assets/rabbitmq-work.png)

RabbitMQ Exchange ve Queue içerisinde barındıran yapıdır. Publish ve Receiver’ı ise biz oluşturuyoruz.

- Broker: Mesajları yönlendiren RabbitMQ sunucusudur.
- Publisher (Producer) Verinin RabbitMQ’ye gönderildiği yer.
- Receiver (Consumer): RabbitMQ Publisher’ları sıraya koyup ilgili Receiver’lara iletiyor.
- Routing key: mesajlarımızı ilgili yerlere göndermek için eklediğimiz anahtar kelimeler, etiketler.
- Exchange: Bu anahtar kelimelere göre ilgili kuyruğa veriyi göndermemizi sağlayan araçtır.En basit tabiriyle yönlendirici diyebiliriz, yani Publisher’dan gelen mesajı ya da mesajları alıp Queue’ya yönlendiren yapı olarak tanımlamak mümkün.Producer/Publisher illa ki Exchange kullanmak zorunda değildir. Doğrudan Queue’ya mesajı iletebilir.
- Queue: Görevi, Consumer’lara (receiver) verileri teker teker göndermek.
- Channels: Publish ve Consumes kısımlarına channel denir.
- Exchange Type: Route key’e göre belirli Queue’lara belirli verileri iletmek Exhange’in göreviydi. Peki exchange type nedir? Gelin buna birlikte göz atalım.

## Exchange Type

### Direct Exchange

![rabbitmq-components](./assets/direct-exchange.png)

Routing Key kullanılarak belirli kuyruğa mesaj yönlendirilir. Consumer bu anahtara göre mesajı alır.

### Fanout Exchange

![rabbitmq-components](./assets/fanout-exchange.png)

Exchange içinde yer alan tüm mesajlar, tüm kuyruklara gönderilir. Routing Key olanlar ise göz ardı edilir.

### Topic Exchange

![rabbitmq-components](./assets/topic-exchange.png)

Verilen anahtara göre farklı farklı kuyruklara yazma işlemini Topic Exchange yapar. Ayrıca kendine göre WildCard desteği bulunur. Örneğin “*” ve “#” ile belirli bir gruba ya da tüm mesajlara erişmek mümkündür.

### Header Exchange

Headers exchange, mesajları header bilgilerine göre kuyruklara yönlendirmek için kullanılır. Header bilgileri, özel bir şekilde tanımlanmış özelliklerdir. Mesaj, header bilgileri ile eşleşen kuyruklara gönderilir.


## RabbitMQ Queue Types

### Classic Queue 

- Mesajlar diske veya belleğe yazılır.
- Tek bir düğüm (node) üzerinde çalışır.
- Düğümlerden biri çökerse kuyruk kaybolabilir (kalıcılık sağlanmazsa).
- Kalıcılığı artırmak için durable (kalıcı) olarak oluşturulabilir.
- Önerilen kullanım: Küçük ve orta ölçekli yükler için uygundur.

### Quorum Queue (Çoğunluk Kuyruğu)
📌 Yüksek güvenilirlik ve veri kaybına karşı dirençli bir yapı sunar.

- Raft konsensüs algoritması ile çalışır.
- Mesajlar birden fazla düğümde çoğaltılır (replicated).
- Eğer bir düğüm çökerse, diğer düğümler kuyruğu çalıştırmaya devam eder.
- Daha fazla disk ve bellek kullanır.
- Önerilen kullanım: Finans, bankacılık gibi kritik mesaj kaybının kabul edilemeyeceği senaryolar.

### Lazy Queue (Tembel Kuyruk)
📌 Büyük mesaj hacimleri için optimize edilmiştir.

- Mesajları doğrudan diske yazar, bellekte tutmaz.
- Ani yük artışlarında sistemin çökmesini önler.
- Kuyruk büyüdüğünde bellek tüketimini artırmaz.
- Önerilen kullanım: Büyük kuyruklar ve nadiren okunan mesajlar için uygundur.

### Stream Queue (Akış Kuyruğu)
📌 Gerçek zamanlı, yüksek performanslı veri akışları için kullanılır.

- Kafka’ya benzer bir yapı sunar.
- Mesajlar diske yazılır ve istenildiği kadar okunabilir.
- Geriye dönük mesaj okuma desteği sağlar.
- Önerilen kullanım: Gerçek zamanlı veri işleme, log analizi, event streaming gibi senaryolar.

### RabbitMQ Queue Properties
Queue, mesajların depolandığı kuyruktur. RabbitMQ’da 3 tür kuyruk vardır:

* Durable Queue: Kuyruk, RabbitMQ sunucusu yeniden başlatıldığında bile korunur. Durable değerini false olarak ayarlarsanız yeniden başlatıldığında silinecektir.
* Exclusive Queue: Kuyruk, sadece oluşturulan bağlantıya özeldir. Bağlantı sonlandırıldığında, kuyruk da silinir.
* Auto-Delete Queue: Kuyruk, en az bir consumer bağlı değilken, son mesajı aldıktan sonra otomatik olarak silinir.