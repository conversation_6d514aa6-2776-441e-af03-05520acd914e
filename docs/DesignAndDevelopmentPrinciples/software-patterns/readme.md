# Software Architectural Pattern

- Vertical Slice Architecture
- Sidecar
- Layered pattern
- Client-server pattern
- Master-slave pattern
- Pipe-filter pattern
- Broker pattern
- Peer-to-peer pattern
- Event-bus pattern
- Model-view-controller pattern
- Blackboard pattern
- Interpreter pattern


## SideCar
A Sidecar is an application which sits in-front of another application running on the same host. The host could be anything from a server or virtual machine, to a container or Kubernetes pod. Sidecars are generally used to intercept traffic to and from the downstream app.

![pattern](./assets/sidecar-pattern.png)

Here’s a few examples:

- Logging
- Performance Monitoring
- Tracing
- Authentication and Authorization
- Retry Policies and Circuit Breakers
- Transparent Encryption

## Microservice

Microservices are used to break down an application into small independent services. A Microservice must have its own dedicated API and database. Microservices can each use different underlying technologies, and they should be independently deployable and scalable.


![pattern](./assets/microservice.png)

## Command Segregation Responsibility Segregation (CQRS)


## Layered pattern

This pattern can be used to structure programs that can be decomposed into groups of subtasks, each of which is at a particular level of abstraction. Each layer provides services to the next higher layer.

The most commonly found 4 layers of a general information system are as follows.

- Presentation layer (also known as UI layer)
- Application layer (also known as service layer)
- Business logic layer (also known as domain layer)
- Data access layer (also known as persistence layer)

    ![pattern](./assets/n-tier-pattern.png)


## Client-Server Pattern

This pattern consists of two parties; a server and multiple clients. The server component will provide services to multiple client components. Clients request services from the server and the server provides relevant services to those clients. 

![pattern](./assets/client-server-pattern.png)


## Master-Slave Pattern

This pattern consists of two parties; master and slaves. The master component distributes the work among identical slave components, and computes a final result from the results which the slaves return.

### Usage
- In database replication, the master database is regarded as the authoritative source, and the slave databases are synchronized to it.
- Peripherals connected to a bus in a computer system (master and slave drives).

![pattern](./assets/master-slave-patter.png)

## Pipe-Filter Pattern

This pattern can be used to structure systems which produce and process a stream of data. Each processing step is enclosed within a filter component. Data to be processed is passed through pipes. These pipes can be used for buffering or for synchronization purposes.

### Usage
- Compilers. The consecutive filters perform lexical analysis, parsing, semantic analysis, and code generation.
- Workflows in bioinformatics.

![pattern](./assets/pipe-filter-pattern.png)

## Broker Pattern - (Publisher-Subscriber)

This pattern is used to structure distributed systems with decoupled components. These components can interact with each other by remote service invocations. A broker component is responsible for the coordination of communication among components.

Servers publish their capabilities (services and characteristics) to a broker. Clients request a service from the broker, and the broker then redirects the client to a suitable service from its registry.

### Usage
- Message broker software such as Apache ActiveMQ, Apache Kafka, RabbitMQ and JBoss Messaging.

![pattern](./assets/broker-pattern.png)

This pattern is used to support Asynchronous communication through a Message Broker, between a Publisher and a Subscriber. Message Brokers contain one or more Topics, which can be Published and Subscribed to.

![pattern](./assets/publisher-subscriber.png)

#### Delivery Mechanisms
There are 2 main Delivery Mechanisms for Publisher-Subscriber:

- Competing Consumers: Each Message published is only consumed once by any Subscriber. This pattern can be used to scale-out the processing of long-running tasks in parallel by lots of Subscriber Worker Services.
- Fanout: Each Message published is consumed by all Subscribers. This pattern is generally used for syncing events or data across lots of downstream applications.

#### When to use?
- Asynchronous communication required
- Scale and load-balance long-running tasks
- Integrate data changes or events across multiple downstream applications


## Peer-to-Peer Pattern

In this pattern, individual components are known as peers. Peers may function both as a client, requesting services from other peers, and as a server, providing services to other peers. A peer may act as a client or as a server or as both, and it can change its role dynamically with time.

### Usage
- File-sharing networks such as Gnutella and G2)
- Multimedia protocols such as P2PTV and PDTP.
- Cryptocurrency-based products such as Bitcoin and Blockchain

![pattern](./assets/peer-to-peer.png)

## Event-bus Pattern

This pattern primarily deals with events and has 4 major components; event source, event listener, channel and event bus. Sources publish messages to particular channels on an event bus. Listeners subscribe to particular channels. Listeners are notified of messages that are published to a channel to which they have subscribed before.

### Usage
- Android development
- Notification services

![pattern](./assets/event-bus.png)

## Model-view-controller Pattern

This pattern, also known as MVC pattern, divides an interactive application in to 3 parts as,

1. model — contains the core functionality and data
2. view — displays the information to the user (more than one view may be defined)
3. controller — handles the input from the user

This is done to separate internal representations of information from the ways information is presented to, and accepted from, the user. It decouples components and allows efficient code reuse.

![pattern](./assets/mvc-pattern.png)

## Blackboard Pattern

This pattern is useful for problems for which no deterministic solution strategies are known. The blackboard pattern consists of 3 main components.

- blackboard — a structured global memory containing objects from the solution space
- knowledge source — specialized modules with their own representation
- control component — selects, configures and executes modules.

All the components have access to the blackboard. Components may produce new data objects that are added to the blackboard. Components look for particular kinds of data on the blackboard, and may find these by pattern matching with the existing knowledge source.

![pattern](./assets/blackboard-pattern.png)

## Interpreter Pattern

This pattern is used for designing a component that interprets programs written in a dedicated language. It mainly specifies how to evaluate lines of programs, known as sentences or expressions written in a particular language. The basic idea is to have a class for each symbol of the language.

### Usage
- Database query languages such as SQL.
- Languages used to describe communication protocols.

![pattern](./assets/interpreter-pattern.png)