# Concurrency vs Parallelism

Simply put,Concurrency, one is about managing multiple tasks simultaneously,Parallelism, while the other is about executing multiple tasks at the same time.

Concurrency refers to the ability of a system to handle multiple tasks at the same time. Parallelism, on the other hand, refers to the ability of a system to execute multiple tasks simultaneously.

Concurrency is about dealing with lots of things at once. Parallelism is about doing lots of things at once.

## Concurrency
Concurrency means an application is making progress on more than one task at the same time.

Concurrency is fundamentally about *structure*. It’s the composition of independently executing processes, where multiple operations are *in progress* at the same time, though not necessarily executing at the same physical instant.

In a computer, the tasks are executed using Central Processing Unit (CPU).

While a single CPU can work on only one task at a time, it achieves concurrency by rapidly switching between tasks.

```mermaid
sequenceDiagram
    participant User1 as User 1
    participant User2 as User 2
    participant Queue as Task Queue
    participant CPU as CPU
    participant ThreadA as Thread A
    participant ThreadB as Thread B
    participant Resource as Shared Resource

    User1->>Queue: Submit Task A
    User2->>Queue: Submit Task B

    Queue->>CPU: Dispatch Task A
    activate ThreadA
    CPU->>ThreadA: Execute Task A (slice 1)
    ThreadA->>Resource: Request access
    Resource-->>ThreadA: Locked

    CPU->>ThreadB: Context switch → Task B
    activate ThreadB
    ThreadB->>Resource: Request access
    Resource-->>ThreadB: Wait (locked)

    CPU->>ThreadA: Context switch → Task A
    ThreadA->>Resource: Release lock
    deactivate ThreadA

    CPU->>ThreadB: Resume Task B
    ThreadB->>Resource: Acquired lock
    ThreadB->>Resource: Write Data
    ThreadB->>Resource: Release lock
    deactivate ThreadB

```

> For example:
    When one thread or process is waiting for I/O operations, database transactions, or external program launches, the CPU can allocate resources to another thread.

```csharp

using System;
using System.Threading;

class Task
{
    private string taskName;

    public Task(string taskName)
    {
        this.taskName = taskName;
    }

    public void Run()
    {
        for (int i = 1; i <= 5; i++)
        {
            Console.WriteLine($"{taskName} - Step {i}");
            try
            {
                Thread.Sleep(500); // Simulate I/O or computation
            }
            catch (ThreadInterruptedException)
            {
                Console.WriteLine($"{taskName} interrupted.");
            }
        }
    }
}

class ConcurrencyExample
{
    static void Main(string[] args)
    {
        Task taskA = new Task("Task A");
        Task taskB = new Task("Task B");
        Task taskC = new Task("Task C");

        Thread thread1 = new Thread(taskA.Run);
        Thread thread2 = new Thread(taskB.Run);
        Thread thread3 = new Thread(taskC.Run);

        thread1.Start();
        thread2.Start();
        thread3.Start();
    }
}


```  

### How does it work?

```mermaid
sequenceDiagram
    participant TaskA as Task A
    participant TaskB as Task B
    participant CPU as 🧠 CPU
    participant Memory as 🧠 Memory (Context Storage)

    Note over TaskA,CPU: CPU starts executing Task A
    TaskA->>CPU: Execute Step 1
    CPU->>Memory: Save Task A context
    CPU->>TaskB: Load Task B context
    TaskB->>CPU: Execute Step 1
    CPU->>Memory: Save Task B context
    CPU->>TaskA: Load Task A context
    TaskA->>CPU: Continue Step 2
    CPU->>Memory: Save Task A context
    CPU->>TaskB: Load Task B context
    TaskB->>CPU: Continue Step 2
    Note over CPU: Repeat rapidly (context switch)

```

## Parallelism

Parallelism means multiple tasks are executed simultaneously.

To achieve parallelism, an application divides its tasks into smaller, independent subtasks. These subtasks are distributed across multiple CPUs, CPU cores, GPU cores, or similar processing units, allowing them to be processed in parallel.

To achieve true parallelism, your application must:

- Utilize more than one thread.
- Ensure each thread is assigned to a separate CPU core or processing unit.

Modern CPUs consist of multiple cores. Each core can independently execute a task. Parallelism divides a problem into smaller parts and assigns each part to a separate core for simultaneous processing.

```mermaid
sequenceDiagram
    participant Problem as 🧩 Problem
    participant Task1 as 🧪 Task 1
    participant Task2 as 🧪 Task 2
    participant Task3 as 🧪 Task 3
    participant Core1 as 🧠 CPU Core 1
    participant Core2 as 🧠 CPU Core 2
    participant Core3 as 🧠 CPU Core 3

    Problem->>Task1: Split into Task 1
    Problem->>Task2: Split into Task 2
    Problem->>Task3: Split into Task 3

    par Run Task 1
        Task1->>Core1: Execute
    and Run Task 2
        Task2->>Core2: Execute
    and Run Task 3
        Task3->>Core3: Execute
    end

```

### How does it work ?

```mermaid
flowchart TB

    Problem[🧩 Main Problem] -->|Divide into parts| T1[Task A]
    Problem -->|Divide into parts| T2[Task B]
    Problem -->|Divide into parts| T3[Task C]
    Problem -->|Divide into parts| T4[Task D]

    subgraph CPU [🖥️ Multi-Core CPU]
        direction LR
        Core1[Core 1] -->|Executes| T1
        Core2[Core 2] -->|Executes| T2
        Core3[Core 3] -->|Executes| T3
        Core4[Core 4] -->|Executes| T4
    end

    T1 --> R1[✅ Result A]
    T2 --> R2[✅ Result B]
    T3 --> R3[✅ Result C]
    T4 --> R4[✅ Result D]

    R1 --> Merge[🔗 Aggregate Results]
    R2 --> Merge
    R3 --> Merge
    R4 --> Merge

    Merge --> Final[🎯 Final Output]

```

| Aspect | Concurrency | Parallelism |
| :--- | :--- | :--- |
| **Definition** | Dealing with multiple tasks by interleaving their execution. Tasks may overlap but may not run simultaneously. | Executing multiple tasks at the same time on separate cores or processors. |
| **Execution** | Achieved through context switching on a single core or thread. | Requires multiple cores or processors to execute tasks simultaneously. |
| **Focus** | Managing multiple tasks and maximizing resource utilization. | Splitting a single task into smaller sub-tasks for simultaneous execution. |
| **Use Case** | Best suited for I/O-bound tasks like handling multiple network requests or file operations. | Ideal for CPU-bound tasks like data processing or machine learning training. |
| **Resource Requirement** | Can be implemented on a single core or thread. | Requires multiple cores or threads. |
| **Outcome** | Improves responsiveness by efficiently managing task switching. | Reduces overall execution time by performing tasks simultaneously. |
| **Examples** | Asynchronous APIs, chat applications, or web servers handling multiple requests. | Video rendering, machine learning training, or scientific simulations. |
| **Analogy** | A single chef multitasking—preparing multiple dishes by working on them in parts. | Multiple chefs working on different dishes at the same time. |

# Async vs Sync Programming

## Synchronous Programming
Synchronous programming is a traditional approach where tasks are executed one after another. Each task must complete before the next one starts. This can lead to inefficiencies, especially when dealing with I/O-bound operations like network requests or file operations.

```mermaid
sequenceDiagram
    participant User
    participant UI_Thread
    participant DB

    User->>UI_Thread: Call DoWork()
    UI_Thread->>DB: Query Data
    Note right of UI_Thread: Thread is blocked\nuntil DB responds
    DB-->>UI_Thread: Return Data
    UI_Thread-->>User: Return Result
```

## Asynchronous Programming
Asynchronous programming allows tasks to be executed concurrently. This is achieved by using non-blocking operations and callbacks. In C#, this is often done using async/await keywords.

```mermaid
sequenceDiagram
    participant User
    participant UI_Thread
    participant SyncContext
    participant ThreadPool
    participant DB

    User->>UI_Thread: Call DoWorkAsync()
    UI_Thread->>SyncContext: Schedule async task
    SyncContext->>ThreadPool: Run async part (Start)
    ThreadPool->>DB: Query Data (non-blocking)
    UI_Thread-->>User: Return Task

    DB-->>ThreadPool: Return Data
    ThreadPool->>SyncContext: Resume continuation
    SyncContext->>UI_Thread: Resume on original thread
    UI_Thread-->>User: Return Final Result

```

# Threads
A thread is the smallest unit of execution in an operating system. In .NET, threads are managed by the Common Language Runtime (CLR). Each thread can execute code independently. When you run a C# application, it starts in a single thread created by the CLR, known as the main thread. Additional threads can be created and used to perform other tasks concurrently.

### Thread Pool
The thread pool is a collection of worker threads maintained by the CLR. When a task is ready to run, a thread from the pool is assigned to it. Once the task is completed, the thread returns to the pool, ready to be reused for another task.

These threads are managed efficiently to handle multiple tasks without the overhead of creating and destroying threads repeatedly.

The Thread Pool: C#’s Engine for Concurrency

At the heart of C#’s concurrency model lies the thread pool — a collection of worker threads managed by the Common Language Runtime (CLR).

```csharp
public void ThreadPoolInfo()
{
// Get current thread pool settings
ThreadPool.GetMinThreads(out int workerThreads, out int completionPortThreads);
Console.WriteLine($"Min worker threads: {workerThreads}");
Console.WriteLine($"Min completion port threads: {completionPortThreads}");
// Get maximum thread pool size
ThreadPool.GetMaxThreads(out workerThreads, out completionPortThreads);
Console.WriteLine($"Max worker threads: {workerThreads}");
Console.WriteLine($"Max completion port threads: {completionPortThreads}");
// Current thread pool usage
ThreadPool.GetAvailableThreads(out workerThreads, out completionPortThreads);
Console.WriteLine($"Available worker threads: {workerThreads}");
Console.WriteLine($"Available completion port threads: {completionPortThreads}");
}
```

The thread pool dynamically grows and shrinks based on demand, with sophisticated heuristics determining when to inject new threads. This avoids the overhead of constant thread creation and destruction.

#### The Scheduler: Orchestrating Thread Execution
Inside the CLR, the thread scheduler decides which threads get CPU time. It implements a preemptive multitasking model with priority levels.

```csharp
// Demonstrating thread priorities
public void ThreadPriorityDemo()
{
var thread = new Thread(() =>
{
Thread.CurrentThread.Name = "Custom Thread";
Console.WriteLine($"Thread {Thread.CurrentThread.Name} is running with priority {Thread.CurrentThread.Priority}");
// Do intensive work
for (int i = 0; i < 1000000000; i++) { }
Console.WriteLine($"Thread {Thread.CurrentThread.Name} completed");
});
// Setting priority (affects scheduling)
thread.Priority = ThreadPriority.AboveNormal;
thread.Start();
Console.WriteLine($"Main thread has priority {Thread.CurrentThread.Priority}");
}
```

Thread priorities influence scheduling decisions but don’t guarantee execution order. The CLR scheduler also incorporates:

1. **Time slicing**: Threads execute for a quantum before being preempted

2. **I/O completion prioritization**: Threads awakened by I/O get priority boosts

3. **Affinitization**: Sometimes binding threads to specific CPU cores for cache locality

### Context Switching: The Cost of Multitasking

When the CLR switches between threads, it performs a context switch — saving the current thread’s state and restoring another’s:

```csharp
// Demonstrating context switching overhead
public void MeasureContextSwitchingOverhead()
{
    const int iterations = 1000000;
    var syncObject = new object();
    var stopwatch = Stopwatch.StartNew();
    // Create two threads that constantly fight for a lock
    var thread1 = new Thread(() => {
        for (int i = 0; i < iterations; i++)
        {
            lock (syncObject)
            {
                // Minimal work
                Thread.SpinWait(1);
            }
        }
    });
    var thread2 = new Thread(() => {
        for (int i = 0; i < iterations; i++)
        {
            lock (syncObject)
            {
                // Minimal work
                Thread.SpinWait(1);
            }
        }
    });
    thread1.Start();
    thread2.Start();
    thread1.Join();
    thread2.Join();
    stopwatch.Stop();
    Console.WriteLine($"Time for {iterations} lock contentions: {stopwatch.ElapsedMilliseconds}ms");
    Console.WriteLine($"Average context switch overhead: {stopwatch.ElapsedMilliseconds / (double)iterations}ms");
}
```
Each context switch involves:
- Saving CPU registers
- Updating memory management structures
- Potentially flushing the translation lookaside buffer (TLB)
- Polluting CPU caches
- Executing hundreds to thousands of CPU instructions

This is why creating too many threads can actually degrade performance — the system spends more time switching contexts than doing useful work.

### Thread Safety

Thread safety means that a piece of code, function, or object can be used by multiple threads at the same time without causing incorrect behavior or data corruption.

If a class or function works correctly when accessed concurrently by multiple threads, it is considered thread-safe.

```csharp
public class ThreadSafeCounter
{
    private int _count = 0;
    private object _lock = new object();

    public void Increment()
    {
        lock (_lock)
        {
            _count++;
        }
    }

    public int Value => _count;
}
```
```csharp
public class ThreadUnsafeCounter
{
    private int _count = 0;

    public void Increment()
    {
        _count++;
    }

    public int Value => _count;
}
```

### Race Conditions
A race condition occurs when the behavior or outcome of a program depends on the relative timing or interleaving of multiple concurrent events, such as threads or processes accessing shared resources without proper synchronization. In multithreaded programs, a race condition happens when multiple threads access and manipulate shared data concurrently, and the final result depends on the unpredictable order in which the threads execute, potentially leading to incorrect or inconsistent program states.

```csharp
public class BankAccount
{
    public int Balance = 100;

    public void Withdraw(int amount)
    {
        if (Balance >= amount)
        {
            Thread.Sleep(10); // Simulate delay
            Balance -= amount;
        }
    }
}

```
```csharp
public class BankAccount
{
    private object _lock = new object();
    public int Balance = 100;

    public void Withdraw(int amount)
    {
        lock (_lock)
        {
            if (Balance >= amount)
            {
                Balance -= amount;
            }
        }
    }
}

```

### Deadlocks
A deadlock occurs when two or more threads are blocked forever, waiting for each other. This usually happens when each thread holds a resource and waits for another resource held by another thread. Deadlocks can be avoided by careful design and use of synchronization primitives like locks, semaphores, and monitors.


### Thread Life Cycle & States

he lifecycle of a thread represents the different states it can be in from creation to termination.

State;	    
- Unstarted : Thread created but not started
- Running : Thread is executing
- WaitSleepJoin : Thread is paused due to sleep/join/wait
- Suspended (legacy) : Thread was manually suspended (deprecated)
- Stopped : Thread has completed execution
- Aborted : Thread was terminated forcefully (not recommended)

# Task Parallel Library (TPL)
The Task Parallel Library (TPL) is a set of APIs in .NET that simplifies parallel programming. It provides a higher-level abstraction over threads and thread pools, making it easier to write concurrent and parallel code. TPL includes classes like Task, Parallel, and PLINQ (Parallel LINQ) to help manage parallelism and concurrency.

```mermaid
sequenceDiagram
    participant UI_Thread
    participant ThreadPool
    participant Task1
    participant Task2
    participant Task3

    UI_Thread->>ThreadPool: Create Task1
    ThreadPool->>Task1: Execute Task1
    UI_Thread->>ThreadPool: Create Task2
    ThreadPool->>Task2: Execute Task2
    UI_Thread->>ThreadPool: Create Task3
    ThreadPool->>Task3: Execute Task3
```

# Tasks
A task represents a unit of work that can be executed asynchronously. Tasks are part of the Task Parallel Library (TPL) in .NET. They provide a higher-level abstraction over threads and thread pools, making it easier to write concurrent and parallel code. Tasks can be created and scheduled using the Task class.    

The key differences:
1. Tasks are higher-level abstractions that may or may not correspond 1:1 with threads
2. Tasks can be scheduled more efficiently by the runtime
3. Tasks support rich composition patterns (continuations, awaiting, WhenAll, etc.)
4. Tasks carry result values and exceptions


> Tasks vs. Threads: The Critical Abstraction <br>
Tasks represent asynchronous operations, while threads are the execution vehicles. 

This abstraction is fundamental to modern C# concurrency:
```csharp
public void TasksVsThreads()
{
    // Traditional thread approach
    var thread = new Thread(() => {
        Console.WriteLine($"Thread ID: {Thread.CurrentThread.ManagedThreadId}");
        Thread.Sleep(1000); // Blocks the thread
        Console.WriteLine("Thread completed");
    });
    thread.Start();
    // Modern task-based approach
    var task = Task.Run(() => {
        Console.WriteLine($"Task running on thread ID: {Thread.CurrentThread.ManagedThreadId}");
        Thread.Sleep(1000); // Still blocks, but only this thread from the pool
        Console.WriteLine("Task completed");
    });
    // Tasks support continuation
    task.ContinueWith(t => {
        Console.WriteLine($"Continuation running on thread ID: {Thread.CurrentThread.ManagedThreadId}");
        Console.WriteLine("Continuation completed");
    });
    // Wait for everything to complete
    thread.Join();
    task.Wait();
}
```

# Async / Await
Async/await is a programming pattern in C# that simplifies writing asynchronous code. It allows developers to write asynchronous code in a synchronous style, making it easier to read and understand. Async/await is built on top of the Task Parallel Library (TPL) and uses the async and await keywords.

When you use `async`/`await`, the C# compiler transforms your method into a state machine. This state machine transformation is the key to async/await’s power. It:

1. Preserves the logical control flow while enabling asynchronous execution
2. Captures local variables in fields of the state machine struct
3. Tracks the current state to know where to resume after awaits
4. Handles exceptions transparently across await boundaries

## SynchronizationContext: The Context Restorer
`SynchronizationContext` ensures that code after `await` runs in the right context (like a UI thread)


## Cancelling an Async Call

Cancellation a async call is handled using the CancellationToken and CancellationTokenSource classes. Suppose you have a web service that processes data, but you want to give the client the ability to cancel the request if it takes too long.

You can cancel a task after a specified time using CancellationTokenSource with a timeout. Automatically cancelling a database query if it exceeds a specified execution time limit.