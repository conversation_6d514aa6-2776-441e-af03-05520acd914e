# Object Oriented Programming (OOP)

Object-oriented programming (OOP) is a programming paradigm based on the concept of "objects", which can contain data and code: data in the form of fields (often known as attributes or properties), and code, in the form of procedures (often known as methods).
In OOP, data and the methods that operate on that data are encapsulated together into objects.

**Key Characteristics of OOP:**
- **Data and Behavior Integration**: Data and the functions (methods) that operate on that data are combined into a single unit
- **Real-World Modeling**: Objects in the program represent real-world entities
- **Code Reusability**: Code written once can be reused in different places
- **Modularity**: Large problems are divided into smaller, manageable parts

OOP facilitates code organization, maintenance, and development processes, especially in large and complex software projects.

## Object

An **Object** is a concrete entity derived from a class that occupies space in memory. Each object has its own:

- **State**: The current values of its properties/fields
- **Behavior**: The operations it can perform (methods)
- **Identity**: A unique reference that distinguishes it from other objects

**Key Points:**
- Objects are created at runtime
- Each object has its own independent state, even if derived from the same class
- Objects can interact with each other through method calls
- Objects are automatically cleaned from memory by the Garbage Collector

**Example:**
```csharp
// Creating two different objects from the Car class
Car car1 = new Car("Toyota", "Camry");  // car1 object
Car car2 = new Car("Honda", "Civic");   // car2 object
// Both are of type Car but have different states
```

## Class

A **Class** is a template or blueprint that defines how objects will be created. A class includes:

- **Fields/Properties**: Data fields that the object will have
- **Methods**: Operations that the object can perform
- **Constructors**: Rules for object creation
- **Access Modifiers**: Keywords that determine access levels

**Key Points:**
- Classes are defined at design-time
- Objects are created from classes at runtime
- Unlimited number of objects can be created from a single class
- Classes support the four fundamental principles of OOP

**Example:**
```csharp
public class Car
{
    // Properties
    public string Brand { get; set; }
    public string Model { get; set; }
    public int Year { get; set; }

    // Constructor
    public Car(string brand, string model, int year)
    {
        Brand = brand;
        Model = model;
        Year = year;
    }

    // Method (behavior)
    public void StartEngine()
    {
        Console.WriteLine($"{Brand} {Model} engine started!");
    }
}
```

## Abstraction

**Abstraction** is the process of hiding complex implementation details while showing only the essential features of an object. It focuses on **what** an object does rather than **how** it does it.

**Key Concepts:**
- **Hide Complexity**: Internal implementation details are hidden from the user
- **Show Essential Features**: Only necessary methods and properties are exposed
- **Simplify Interface**: Provides a clean and simple way to interact with objects
- **Reduce Coupling**: Minimizes dependencies between different parts of code

**Benefits:**
- **Easier to Use**: Users don't need to understand complex internal workings
- **Maintainability**: Implementation can be changed without affecting client code
- **Code Reusability**: Abstract components can be reused in different contexts
- **Focus on Design**: Developers can focus on what objects should do, not how

### Abstract Classes

An **Abstract Class** is a class that cannot be instantiated directly and is designed to be inherited by other classes. It can contain both abstract and concrete members.

**Characteristics:**
- Cannot be instantiated with the `new` keyword
- Can contain abstract methods (without implementation)
- Can contain concrete methods (with implementation)
- Can have constructors, fields, and properties
- Must be inherited by derived classes
- Derived classes must implement all abstract methods

**Example:**
```csharp
public abstract class Shape
{
    // Concrete property
    public string Color { get; set; }

    // Concrete method
    public void SetColor(string color)
    {
        Color = color;
    }

    // Abstract method - must be implemented by derived classes
    public abstract double CalculateArea();

    // Abstract method
    public abstract double CalculatePerimeter();
}

public class Circle : Shape
{
    public double Radius { get; set; }

    public Circle(double radius)
    {
        Radius = radius;
    }

    // Must implement abstract methods
    public override double CalculateArea()
    {
        return Math.PI * Radius * Radius;
    }

    public override double CalculatePerimeter()
    {
        return 2 * Math.PI * Radius;
    }
}

public class Rectangle : Shape
{
    public double Width { get; set; }
    public double Height { get; set; }

    public Rectangle(double width, double height)
    {
        Width = width;
        Height = height;
    }

    public override double CalculateArea()
    {
        return Width * Height;
    }

    public override double CalculatePerimeter()
    {
        return 2 * (Width + Height);
    }
}
```

### Interfaces

An **Interface** defines a contract that implementing classes must follow. It contains only method signatures, properties, events, and indexers without implementation.

**Characteristics:**
- Cannot be instantiated
- All members are implicitly public and abstract
- Cannot contain fields or constructors
- A class can implement multiple interfaces
- Supports multiple inheritance through interfaces

**Example:**
```csharp
public interface IDrawable
{
    void Draw();
    void Move(int x, int y);
}

public interface IResizable
{
    void Resize(double factor);
}

public class Button : IDrawable, IResizable
{
    public int X { get; set; }
    public int Y { get; set; }
    public double Size { get; set; }

    public void Draw()
    {
        Console.WriteLine($"Drawing button at ({X}, {Y})");
    }

    public void Move(int x, int y)
    {
        X = x;
        Y = y;
    }

    public void Resize(double factor)
    {
        Size *= factor;
    }
}
```

### Abstract Class vs Interface

| **Abstract Class** | **Interface** |
|-------------------|---------------|
| Can have both abstract and concrete methods | Only method signatures (until C# 8.0) |
| Can have constructors | Cannot have constructors |
| Can have fields | Cannot have fields |
| Single inheritance only | Multiple inheritance supported |
| Can have access modifiers | All members are public |
| Use when classes share common code | Use when classes share common behavior |


## Encapsulation

**Encapsulation** is the process of grouping data **(fields/properties)** and operations related to this data **(methods)** into a single unit **(class)** in object-oriented programming.
This structure hides the internal structure and complexity of the object from the outside world. The object does not allow direct access to its data; instead, access to this data is only possible through specified methods.

Encapsulation both preserves the integrity of the data and isolates the object's behavior from external influences, thereby enhancing the security and stability of the system.
This mechanism also allows us to control how the class appears to the outside world (its interface) and to perform operations on the object in a more controlled manner.


**Key Concepts:**
- **Data Hiding**: Internal data is hidden from outside access
- **Controlled Access**: Access to data is controlled through methods (getters/setters)
- **Information Security**: Prevents unauthorized access and modification
- **Validation**: Ensures data integrity through controlled input validation

**Benefits:**
- **Data Protection**: Prevents external code from directly modifying internal state
- **Maintainability**: Internal implementation can be changed without affecting external code
- **Debugging**: Easier to track data changes through controlled access points
- **Code Organization**: Related data and methods are grouped together

**Implementation in C#:**
```csharp
public class BankAccount
{
    // Private fields (encapsulated data)
    private decimal balance;
    private string accountNumber;

    // Public properties (controlled access)
    public string AccountNumber
    {
        get { return accountNumber; }
        private set { accountNumber = value; } // Only class can set
    }

    public decimal Balance
    {
        get { return balance; }
        private set
        {
            if (value >= 0)
                balance = value;
            else
                throw new ArgumentException("Balance cannot be negative");
        }
    }

    // Constructor
    public BankAccount(string accountNumber, decimal initialBalance)
    {
        AccountNumber = accountNumber;
        Balance = initialBalance;
    }

    // Public methods (controlled operations)
    public void Deposit(decimal amount)
    {
        if (amount > 0)
        {
            balance += amount;
            Console.WriteLine($"Deposited: ${amount}. New balance: ${balance}");
        }
        else
        {
            throw new ArgumentException("Deposit amount must be positive");
        }
    }

    public bool Withdraw(decimal amount)
    {
        if (amount > 0 && amount <= balance)
        {
            balance -= amount;
            Console.WriteLine($"Withdrawn: ${amount}. New balance: ${balance}");
            return true;
        }
        else
        {
            Console.WriteLine("Invalid withdrawal amount or insufficient funds");
            return false;
        }
    }
}
```

**Access Levels:**
- **private**: Only accessible within the same class
- **protected**: Accessible within the class and its derived classes
- **internal**: Accessible within the same assembly
- **public**: Accessible from anywhere

## Inheritance

**Inheritance** is a mechanism that allows a class to acquire properties and methods from another class. It establishes an "is-a" relationship between classes, promoting code reuse and creating hierarchical relationships.

**Key Concepts:**
- **Base/Parent Class**: The class being inherited from
- **Derived/Child Class**: The class that inherits from the base class
- **Code Reuse**: Derived classes automatically get base class members
- **Hierarchical Structure**: Creates a tree-like class organization
- **Specialization**: Derived classes can add new features or modify existing ones

**Types of Inheritance:**
- **Single Inheritance**: A class inherits from one base class (C# supports this)
- **Multiple Inheritance**: A class inherits from multiple classes (not supported in C# for classes)
- **Multilevel Inheritance**: A chain of inheritance (A → B → C)
- **Hierarchical Inheritance**: Multiple classes inherit from one base class

**Benefits:**
- **Code Reusability**: Avoid writing duplicate code
- **Maintainability**: Changes in base class affect all derived classes
- **Extensibility**: Easy to add new features through inheritance
- **Polymorphism**: Enables method overriding and dynamic binding

**Example:**
```csharp
// Base class
public class Vehicle
{
    public string Brand { get; set; }
    public string Model { get; set; }
    public int Year { get; set; }

    public Vehicle(string brand, string model, int year)
    {
        Brand = brand;
        Model = model;
        Year = year;
    }

    public virtual void Start()
    {
        Console.WriteLine($"{Brand} {Model} is starting...");
    }

    public void Stop()
    {
        Console.WriteLine($"{Brand} {Model} has stopped.");
    }
}

// Derived class
public class Car : Vehicle
{
    public int NumberOfDoors { get; set; }

    public Car(string brand, string model, int year, int doors)
        : base(brand, model, year) // Call base constructor
    {
        NumberOfDoors = doors;
    }

    // Override base method
    public override void Start()
    {
        Console.WriteLine($"Car {Brand} {Model} engine started with key.");
    }

    // New method specific to Car
    public void OpenTrunk()
    {
        Console.WriteLine("Trunk opened.");
    }
}

// Another derived class
public class Motorcycle : Vehicle
{
    public bool HasSidecar { get; set; }

    public Motorcycle(string brand, string model, int year, bool sidecar)
        : base(brand, model, year)
    {
        HasSidecar = sidecar;
    }

    public override void Start()
    {
        Console.WriteLine($"Motorcycle {Brand} {Model} started with kick/button.");
    }

    public void Wheelie()
    {
        Console.WriteLine("Performing a wheelie!");
    }
}
```

**Usage Example:**
```csharp
Vehicle vehicle = new Vehicle("Generic", "Vehicle", 2023);
Car car = new Car("Toyota", "Camry", 2023, 4);
Motorcycle bike = new Motorcycle("Harley", "Davidson", 2023, false);

vehicle.Start();  // Generic start
car.Start();      // Car-specific start
bike.Start();     // Motorcycle-specific start

car.OpenTrunk();  // Car-specific method
bike.Wheelie();   // Motorcycle-specific method
```

**Important Keywords:**
- **virtual**: Allows a method to be overridden in derived classes
- **override**: Overrides a virtual method from the base class
- **base**: Refers to the base class instance
- **sealed**: Prevents a class from being inherited
- **abstract**: Forces derived classes to implement specific methods

## Polymorphism

**Polymorphism** means "many forms" and refers to the ability of objects of different types to be treated as instances of the same type through a common interface. It allows a single interface to represent different underlying forms (data types).

**Key Concepts:**
- **One Interface, Multiple Implementations**: Same method name, different behaviors
- **Runtime Decision**: The actual method called is determined at runtime
- **Flexibility**: Code can work with objects of different types without knowing their specific type
- **Extensibility**: New types can be added without modifying existing code

**Types of Polymorphism:**

### 1. Compile-Time Polymorphism (Static Polymorphism)
**Method Overloading**: Multiple methods with the same name but different parameters.

```csharp
public class Calculator
{
    // Method overloading - same name, different parameters
    public int Add(int a, int b)
    {
        return a + b;
    }

    public double Add(double a, double b)
    {
        return a + b;
    }

    public int Add(int a, int b, int c)
    {
        return a + b + c;
    }

    public string Add(string a, string b)
    {
        return a + b;
    }
}
```

**Operator Overloading**: Giving additional meanings to operators for user-defined types.

```csharp
public class Point
{
    public int X { get; set; }
    public int Y { get; set; }

    public Point(int x, int y)
    {
        X = x;
        Y = y;
    }

    // Operator overloading
    public static Point operator +(Point p1, Point p2)
    {
        return new Point(p1.X + p2.X, p1.Y + p2.Y);
    }

    public override string ToString()
    {
        return $"({X}, {Y})";
    }
}

// Usage
Point p1 = new Point(1, 2);
Point p2 = new Point(3, 4);
Point p3 = p1 + p2; // Uses overloaded + operator
```

### 2. Runtime Polymorphism (Dynamic Polymorphism)
**Method Overriding**: Derived classes provide specific implementations of virtual methods.

```csharp
public abstract class Animal
{
    public string Name { get; set; }

    public Animal(string name)
    {
        Name = name;
    }

    // Virtual method - can be overridden
    public virtual void MakeSound()
    {
        Console.WriteLine($"{Name} makes a sound");
    }

    // Abstract method - must be overridden
    public abstract void Move();
}

public class Dog : Animal
{
    public Dog(string name) : base(name) { }

    public override void MakeSound()
    {
        Console.WriteLine($"{Name} barks: Woof! Woof!");
    }

    public override void Move()
    {
        Console.WriteLine($"{Name} runs on four legs");
    }
}

public class Cat : Animal
{
    public Cat(string name) : base(name) { }

    public override void MakeSound()
    {
        Console.WriteLine($"{Name} meows: Meow! Meow!");
    }

    public override void Move()
    {
        Console.WriteLine($"{Name} walks gracefully");
    }
}

public class Bird : Animal
{
    public Bird(string name) : base(name) { }

    public override void MakeSound()
    {
        Console.WriteLine($"{Name} chirps: Tweet! Tweet!");
    }

    public override void Move()
    {
        Console.WriteLine($"{Name} flies in the sky");
    }
}
```

**Polymorphic Usage:**
```csharp
// Polymorphism in action
Animal[] animals = {
    new Dog("Buddy"),
    new Cat("Whiskers"),
    new Bird("Tweety")
};

foreach (Animal animal in animals)
{
    animal.MakeSound(); // Calls the appropriate overridden method
    animal.Move();      // Calls the appropriate overridden method
    Console.WriteLine();
}

// Output:
// Buddy barks: Woof! Woof!
// Buddy runs on four legs
//
// Whiskers meows: Meow! Meow!
// Whiskers walks gracefully
//
// Tweety chirps: Tweet! Tweet!
// Tweety flies in the sky
```

**Interface-based Polymorphism:**
```csharp
public interface IShape
{
    double CalculateArea();
    void Draw();
}

public class Circle : IShape
{
    public double Radius { get; set; }

    public Circle(double radius)
    {
        Radius = radius;
    }

    public double CalculateArea()
    {
        return Math.PI * Radius * Radius;
    }

    public void Draw()
    {
        Console.WriteLine($"Drawing a circle with radius {Radius}");
    }
}

public class Rectangle : IShape
{
    public double Width { get; set; }
    public double Height { get; set; }

    public Rectangle(double width, double height)
    {
        Width = width;
        Height = height;
    }

    public double CalculateArea()
    {
        return Width * Height;
    }

    public void Draw()
    {
        Console.WriteLine($"Drawing a rectangle {Width}x{Height}");
    }
}

// Polymorphic usage with interfaces
IShape[] shapes = {
    new Circle(5),
    new Rectangle(4, 6),
    new Circle(3)
};

foreach (IShape shape in shapes)
{
    shape.Draw();
    Console.WriteLine($"Area: {shape.CalculateArea():F2}");
    Console.WriteLine();
}
```

**Benefits of Polymorphism:**
- **Code Flexibility**: Write code that works with multiple types
- **Maintainability**: Easy to add new types without changing existing code
- **Extensibility**: New implementations can be added seamlessly
- **Abstraction**: Client code doesn't need to know specific types

## Access Modifiers

- public: The member is accessible from any code that can access the class.
- private: The member is only accessible within the class itself.
- protected: The member is accessible within the class and its derived classes.
- internal: The member is accessible within the assembly (i.e., the .dll or .exe).
- protected internal: The member is accessible within the assembly and its derived classes.