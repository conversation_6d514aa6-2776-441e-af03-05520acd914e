# Object Oriented Programming (OOP)

Object-Oriented Programming (OOP), yazılım geliştirmede kullanılan bir programlama paradigmasıdır. Bu paradigma, programları **nesneler** (objects) etrafında organize eder ve gerçek dünya varlıklarını modelleyerek yazılım geliştirme sürecini daha anlaşılır hale getirir.

**OOP'nin Temel Özellikleri:**
- **Veri ve Davranışın Birleşimi**: <PERSON><PERSON><PERSON> (data) ve bu veriler üzerinde işlem yapan fonksiyonlar (methods) tek bir birim içinde toplanır
- **Gerçek Dünya Modellemesi**: <PERSON>daki nesneler, gerçek hayattaki varlıkları temsil eder
- **Kod Yeniden Kullanımı**: Bir kez yazılan kod, farklı yerlerde tekrar kullanılabilir
- **Modülerlik**: <PERSON><PERSON><PERSON><PERSON><PERSON> problemler, küçük ve yönetilebilir parçalara b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

O<PERSON>, özellikle büyük ve karmaşık yazılım projelerinde kod organizasyonu, bakım ve geliştirme süreçlerini kolaylaştırır.

## Object (Nesne)

**Object (Nesne)**, bir class'tan türetilen ve bellekte yer kaplayan somut bir varlıktır. Her nesne, kendine özgü:

- **State (Durum)**: Nesnenin o anki özellik değerleri (properties/fields)
- **Behavior (Davranış)**: Nesnenin yapabileceği işlemler (methods)
- **Identity (Kimlik)**: Nesneyi diğer nesnelerden ayıran benzersiz referans

**Önemli Noktalar:**
- Nesneler runtime'da (çalışma zamanında) oluşturulur
- Her nesne, aynı class'tan türetilse bile kendi bağımsız durumuna sahiptir
- Nesneler birbirleriyle method çağrıları ile etkileşim kurabilir
- Garbage Collector tarafından otomatik olarak bellekten temizlenirler

**Örnek:**
```csharp
// Car class'ından iki farklı nesne oluşturma
Car car1 = new Car("Toyota", "Camry");  // car1 nesnesi
Car car2 = new Car("Honda", "Civic");   // car2 nesnesi
// Her ikisi de Car türünde ama farklı durumlara sahip
```

## Class (Sınıf)

**Class (Sınıf)**, nesnelerin nasıl oluşturulacağını tanımlayan bir şablon veya blueprint'tir. Class, aşağıdakileri içerir:

- **Fields/Properties**: Nesnenin sahip olacağı veri alanları
- **Methods**: Nesnenin gerçekleştirebileceği işlemler
- **Constructors**: Nesne oluşturma kuralları
- **Access Modifiers**: Erişim seviyelerini belirleyen anahtar kelimeler

**Önemli Noktalar:**
- Class'lar tasarım zamanında (design-time) tanımlanır
- Nesneler çalışma zamanında (runtime) class'lardan oluşturulur
- Bir class'tan sınırsız sayıda nesne oluşturulabilir
- Class'lar OOP'nin dört temel prensibini destekler

**Örnek:**
```csharp
public class Car
{
    // Properties (özellikler)
    public string Brand { get; set; }
    public string Model { get; set; }
    public int Year { get; set; }

    // Constructor (yapıcı method)
    public Car(string brand, string model, int year)
    {
        Brand = brand;
        Model = model;
        Year = year;
    }

    // Method (davranış)
    public void StartEngine()
    {
        Console.WriteLine($"{Brand} {Model} motoru çalıştırıldı!");
    }
}
```

## Abstraction
Show only the necessary things.
### Interface


## Encapsulation
Encapsulation is wrapping/binding up of data and member functions in single unit. In simple, abstraction is hiding the implementation and encapsulation is to hide data.

Wrapping up data into a single unit. Encapsulation hides the complexity of the way the object was implemented . Encapsulation in object-oriented programming (OOP) is a mechanism of hiding the internal details (implementation) of an object from other objects and the outside world.
Encapsulation allows an object to control access to its data and methods, which can improve the security and stability of the system.

## Inheritance

Inheritance allows us to define a class in terms of another class, which makes it easier to create and maintain an application. This also provides an opportunity to reuse the code functionality and speeds up implementation time.

Inheritance is a fundamental concept in OOP, allowing you to create new classes by reusing and extending existing classes. The new class, called the derived or child class, inherits the properties and methods of the base or parent class. This promotes code reuse and a hierarchical organization of classes.

```csharp
public class Parent
{
  public string Method1()
  {

  }
}
public class Child :Parent 
{
  public string Method2()
  {

  }
}
```

## Polymorphism

When an object exhibits different behavior in different situation

Polymorphism means single name & multiple meaning & whenever we try to access that particular name there must be a binding between that call & between that function. So there are two types of binding to call that function.

Method Overloading — Compile time

Method Overriding — Runtime

## Access Modifiers

- public: The member is accessible from any code that can access the class.
- private: The member is only accessible within the class itself.
- protected: The member is accessible within the class and its derived classes.
- internal: The member is accessible within the assembly (i.e., the .dll or .exe).
- protected internal: The member is accessible within the assembly and its derived classes.