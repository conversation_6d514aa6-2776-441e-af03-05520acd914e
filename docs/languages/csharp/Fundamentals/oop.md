# Object Oriented Programming (OOP)

Object-Oriented Programming (OOP) is a programming paradigm that organizes software design around **objects** rather than functions and logic. It is based on the concept of "objects", which are entities that combine both **data** (attributes/properties) and **behavior** (methods/functions) into a single unit.

**Key Characteristics of OOP:**
- **Modularity**: Code is organized into discrete objects that can be developed and maintained independently
- **Reusability**: Objects and classes can be reused across different parts of an application
- **Maintainability**: Changes to one object typically don't affect other objects
- **Scalability**: Large applications can be built by combining smaller, manageable objects

OOP models real-world entities and their interactions, making it easier to understand, design, and maintain complex software systems.

## Object

An **Object** is a concrete instance of a class that exists in memory during program execution. It represents a specific entity with:

- **State**: The current values of its attributes/properties
- **Behavior**: The actions it can perform through its methods
- **Identity**: A unique reference that distinguishes it from other objects

**Key Points:**
- Objects are created (instantiated) from classes at runtime
- Each object has its own copy of instance variables
- Objects can interact with each other through method calls
- Objects encapsulate both data and the operations that can be performed on that data

**Example:**
```csharp
// Creating objects from a class
Car myCar = new Car("Toyota", "Camry", 2023);
Car yourCar = new Car("Honda", "Civic", 2022);
// myCar and yourCar are two different objects with different states
```

## Class

A **Class** is a blueprint, template, or prototype that defines the structure and behavior of objects. It serves as a user-defined data type that specifies:

- **Attributes/Properties**: What data the objects will store
- **Methods**: What operations the objects can perform
- **Constructors**: How objects are initialized
- **Access Modifiers**: How the class members can be accessed

**Key Points:**
- Classes are design-time constructs (templates)
- Objects are runtime instances of classes
- A single class can be used to create multiple objects
- Classes define the common properties and behaviors shared by all their instances
- Classes support the four pillars of OOP: Encapsulation, Inheritance, Polymorphism, and Abstraction

**Example:**
```csharp
public class Car
{
    // Properties (attributes)
    public string Brand { get; set; }
    public string Model { get; set; }
    public int Year { get; set; }

    // Constructor
    public Car(string brand, string model, int year)
    {
        Brand = brand;
        Model = model;
        Year = year;
    }

    // Method (behavior)
    public void StartEngine()
    {
        Console.WriteLine($"{Brand} {Model} engine started!");
    }
}

## Abstraction
Show only the necessary things.
### Interface


## Encapsulation
Encapsulation is wrapping/binding up of data and member functions in single unit. In simple, abstraction is hiding the implementation and encapsulation is to hide data.

Wrapping up data into a single unit. Encapsulation hides the complexity of the way the object was implemented . Encapsulation in object-oriented programming (OOP) is a mechanism of hiding the internal details (implementation) of an object from other objects and the outside world.
Encapsulation allows an object to control access to its data and methods, which can improve the security and stability of the system.

## Inheritance

Inheritance allows us to define a class in terms of another class, which makes it easier to create and maintain an application. This also provides an opportunity to reuse the code functionality and speeds up implementation time.

Inheritance is a fundamental concept in OOP, allowing you to create new classes by reusing and extending existing classes. The new class, called the derived or child class, inherits the properties and methods of the base or parent class. This promotes code reuse and a hierarchical organization of classes.

```csharp
public class Parent
{
  public string Method1()
  {

  }
}
public class Child :Parent 
{
  public string Method2()
  {

  }
}
```

## Polymorphism

When an object exhibits different behavior in different situation

Polymorphism means single name & multiple meaning & whenever we try to access that particular name there must be a binding between that call & between that function. So there are two types of binding to call that function.

Method Overloading — Compile time

Method Overriding — Runtime

## Access Modifiers

- public: The member is accessible from any code that can access the class.
- private: The member is only accessible within the class itself.
- protected: The member is accessible within the class and its derived classes.
- internal: The member is accessible within the assembly (i.e., the .dll or .exe).
- protected internal: The member is accessible within the assembly and its derived classes.