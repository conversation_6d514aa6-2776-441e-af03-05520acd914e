# Object Oriented Programming (OOP)

Object-oriented programming (OOP) is a programming paradigm based on the concept of "objects", which can contain data and code: data in the form of fields (often known as attributes or properties), and code, in the form of procedures (often known as methods).
In OOP, data and the methods that operate on that data are encapsulated together into objects.

**Key Characteristics of OOP:**
- **Data and Behavior Integration**: Data and the functions (methods) that operate on that data are combined into a single unit
- **Real-World Modeling**: Objects in the program represent real-world entities
- **Code Reusability**: Code written once can be reused in different places
- **Modularity**: Large problems are divided into smaller, manageable parts

OOP facilitates code organization, maintenance, and development processes, especially in large and complex software projects.

## Object

An **Object** is a concrete entity derived from a class that occupies space in memory. Each object has its own:

- **State**: The current values of its properties/fields
- **Behavior**: The operations it can perform (methods)
- **Identity**: A unique reference that distinguishes it from other objects

**Key Points:**
- Objects are created at runtime
- Each object has its own independent state, even if derived from the same class
- Objects can interact with each other through method calls
- Objects are automatically cleaned from memory by the Garbage Collector

**Example:**
```csharp
// Creating two different objects from the Car class
Car car1 = new Car("Toyota", "Camry");  // car1 object
Car car2 = new Car("Honda", "Civic");   // car2 object
// Both are of type Car but have different states
```

## Class

A **Class** is a template or blueprint that defines how objects will be created. A class includes:

- **Fields/Properties**: Data fields that the object will have
- **Methods**: Operations that the object can perform
- **Constructors**: Rules for object creation
- **Access Modifiers**: Keywords that determine access levels

**Key Points:**
- Classes are defined at design-time
- Objects are created from classes at runtime
- Unlimited number of objects can be created from a single class
- Classes support the four fundamental principles of OOP

**Example:**
```csharp
public class Car
{
    // Properties
    public string Brand { get; set; }
    public string Model { get; set; }
    public int Year { get; set; }

    // Constructor
    public Car(string brand, string model, int year)
    {
        Brand = brand;
        Model = model;
        Year = year;
    }

    // Method (behavior)
    public void StartEngine()
    {
        Console.WriteLine($"{Brand} {Model} engine started!");
    }
}
```

## Abstraction

**Abstraction** is the process of hiding complex implementation details while showing only the essential features of an object. It focuses on **what** an object does rather than **how** it does it.

**Key Concepts:**
- **Hide Complexity**: Internal implementation details are hidden from the user
- **Show Essential Features**: Only necessary methods and properties are exposed
- **Simplify Interface**: Provides a clean and simple way to interact with objects
- **Reduce Coupling**: Minimizes dependencies between different parts of code

**Benefits:**
- **Easier to Use**: Users don't need to understand complex internal workings
- **Maintainability**: Implementation can be changed without affecting client code
- **Code Reusability**: Abstract components can be reused in different contexts
- **Focus on Design**: Developers can focus on what objects should do, not how

### Abstract Classes

An **Abstract Class** is a class that cannot be instantiated directly and is designed to be inherited by other classes. It can contain both abstract and concrete members.

**Characteristics:**
- Cannot be instantiated with the `new` keyword
- Can contain abstract methods (without implementation)
- Can contain concrete methods (with implementation)
- Can have constructors, fields, and properties
- Must be inherited by derived classes
- Derived classes must implement all abstract methods

**Example:**
```csharp
public abstract class Shape
{
    // Concrete property
    public string Color { get; set; }

    // Concrete method
    public void SetColor(string color)
    {
        Color = color;
    }

    // Abstract method - must be implemented by derived classes
    public abstract double CalculateArea();

    // Abstract method
    public abstract double CalculatePerimeter();
}

public class Circle : Shape
{
    public double Radius { get; set; }

    public Circle(double radius)
    {
        Radius = radius;
    }

    // Must implement abstract methods
    public override double CalculateArea()
    {
        return Math.PI * Radius * Radius;
    }

    public override double CalculatePerimeter()
    {
        return 2 * Math.PI * Radius;
    }
}

public class Rectangle : Shape
{
    public double Width { get; set; }
    public double Height { get; set; }

    public Rectangle(double width, double height)
    {
        Width = width;
        Height = height;
    }

    public override double CalculateArea()
    {
        return Width * Height;
    }

    public override double CalculatePerimeter()
    {
        return 2 * (Width + Height);
    }
}
```

### Interfaces

An **Interface** defines a contract that implementing classes must follow. It contains only method signatures, properties, events, and indexers without implementation.

**Characteristics:**
- Cannot be instantiated
- All members are implicitly public and abstract
- Cannot contain fields or constructors
- A class can implement multiple interfaces
- Supports multiple inheritance through interfaces

**Example:**
```csharp
public interface IDrawable
{
    void Draw();
    void Move(int x, int y);
}

public interface IResizable
{
    void Resize(double factor);
}

public class Button : IDrawable, IResizable
{
    public int X { get; set; }
    public int Y { get; set; }
    public double Size { get; set; }

    public void Draw()
    {
        Console.WriteLine($"Drawing button at ({X}, {Y})");
    }

    public void Move(int x, int y)
    {
        X = x;
        Y = y;
    }

    public void Resize(double factor)
    {
        Size *= factor;
    }
}
```

### Abstract Class vs Interface

| **Abstract Class** | **Interface** |
|-------------------|---------------|
| Can have both abstract and concrete methods | Only method signatures (until C# 8.0) |
| Can have constructors | Cannot have constructors |
| Can have fields | Cannot have fields |
| Single inheritance only | Multiple inheritance supported |
| Can have access modifiers | All members are public |
| Use when classes share common code | Use when classes share common behavior |


## Encapsulation
Encapsulation is wrapping/binding up of data and member functions in single unit. In simple, abstraction is hiding the implementation and encapsulation is to hide data.

Wrapping up data into a single unit. Encapsulation hides the complexity of the way the object was implemented . Encapsulation in object-oriented programming (OOP) is a mechanism of hiding the internal details (implementation) of an object from other objects and the outside world.
Encapsulation allows an object to control access to its data and methods, which can improve the security and stability of the system.

## Inheritance

Inheritance allows us to define a class in terms of another class, which makes it easier to create and maintain an application. This also provides an opportunity to reuse the code functionality and speeds up implementation time.

Inheritance is a fundamental concept in OOP, allowing you to create new classes by reusing and extending existing classes. The new class, called the derived or child class, inherits the properties and methods of the base or parent class. This promotes code reuse and a hierarchical organization of classes.

```csharp
public class Parent
{
  public string Method1()
  {

  }
}
public class Child :Parent 
{
  public string Method2()
  {

  }
}
```

## Polymorphism

When an object exhibits different behavior in different situation

Polymorphism means single name & multiple meaning & whenever we try to access that particular name there must be a binding between that call & between that function. So there are two types of binding to call that function.

Method Overloading — Compile time

Method Overriding — Runtime

## Access Modifiers

- public: The member is accessible from any code that can access the class.
- private: The member is only accessible within the class itself.
- protected: The member is accessible within the class and its derived classes.
- internal: The member is accessible within the assembly (i.e., the .dll or .exe).
- protected internal: The member is accessible within the assembly and its derived classes.