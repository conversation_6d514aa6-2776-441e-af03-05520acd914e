# Object Oriented Programming (OOP)

Object-oriented programming (OOP) is a programming paradigm based on the concept of "objects", which can contain data and code: data in the form of fields (often known as attributes or properties), and code, in the form of procedures (often known as methods).
In OOP, data and the methods that operate on that data are encapsulated together into objects.

## Object
 An Object is an instance.
 Objects, in turn, are instances of classes.

## Class
A class is a type, or blueprint which contains the data members and methods
a class is a blueprint or template for creating objects.

## Abstraction
Show only the necessary things.
### Interface


## Encapsulation
Encapsulation is wrapping/binding up of data and member functions in single unit. In simple, abstraction is hiding the implementation and encapsulation is to hide data.

Wrapping up data into a single unit. Encapsulation hides the complexity of the way the object was implemented . Encapsulation in object-oriented programming (OOP) is a mechanism of hiding the internal details (implementation) of an object from other objects and the outside world.
Encapsulation allows an object to control access to its data and methods, which can improve the security and stability of the system.

## Inheritance

Inheritance allows us to define a class in terms of another class, which makes it easier to create and maintain an application. This also provides an opportunity to reuse the code functionality and speeds up implementation time.

Inheritance is a fundamental concept in OOP, allowing you to create new classes by reusing and extending existing classes. The new class, called the derived or child class, inherits the properties and methods of the base or parent class. This promotes code reuse and a hierarchical organization of classes.

```csharp
public class Parent
{
  public string Method1()
  {

  }
}
public class Child :Parent 
{
  public string Method2()
  {

  }
}
```

## Polymorphism

When an object exhibits different behavior in different situation

Polymorphism means single name & multiple meaning & whenever we try to access that particular name there must be a binding between that call & between that function. So there are two types of binding to call that function.

Method Overloading — Compile time

Method Overriding — Runtime

## Access Modifiers

- public: The member is accessible from any code that can access the class.
- private: The member is only accessible within the class itself.
- protected: The member is accessible within the class and its derived classes.
- internal: The member is accessible within the assembly (i.e., the .dll or .exe).
- protected internal: The member is accessible within the assembly and its derived classes.