# Garbage Collection

Programlama dillerinde bellek yönetimi genellikle <PERSON>ack (Yığın) ve Heap (Yığın Bellek) olarak ikiye ayrılır:

## Stack Belleği

Stack: Fonksiyon çağrılarında kullanılan yerel değişkenler burada tutulur. Fonksiyon sona erdiğinde otomatik olarak bellek serbest bırakılır.

Her thread için ayrı olarak ayrılır.

Fonksiyon çağrılarında, lokal değişkenlerin saklanması için kullanılır.

Fonksiyon tamamlandığında ilgili bellek otomatik olarak serbest bırakılır.

Hızlıdır ve yönetimi kolaydır.

## Heap

Heap: new veya malloc gibi anahtar kelimelerle dinamik olarak nesneler oluşturulur. Bellek elle serbest bırakılmazsa kullanılmayan nesneler bellekte kalır (bellek sızıntısı oluşabilir).

Dinamik bellek tahsisi için kullanılı<PERSON> (örnek: new, malloc).

Bellek serbest bırakımı programcıya bırakılmıştır (C/C++) veya çöp toplayıcıya (Java, C# gibi).

Daha esnektir ancak yönetimi karmaşıktır.

## Reference Counted GC (Referans Sayımlı Çöp Toplayıcı):
Her nesneye kaç referans olduğunu sayar.

Referans sayısı sıfır olursa nesne bellekten temizlenir.

Ancak dairesel (circular) referansları algılayamaz, bu yüzden birbirine referans veren nesneler bellekten temizlenemez.

## Mark and Sweep GC (İşaretle ve Temizle):
- Mark (İşaretleme) Aşaması: Kullanılan nesneler bulunur ve işaretlenir.

- Sweep (Temizleme) Aşaması: İşaretlenmemiş (kullanılmayan) nesneler bellekten silinir.

Dairesel referanslar burada sorun teşkil etmez çünkü kullanılmayan nesneler kök referanslardan ulaşılabilir değilse temizlenir.

## Mark-Sweep-Compact (İşaretle, Temizle, Sıkıştır):
Bellekte parçalanmayı önlemek için, nesneler yeniden düzenlenerek sıkıştırılır.

Bu işlem daha uzun süreli duraklamalara (GC Pause) neden olabilir.

## Mark and Copy:
Bellek ikiye ayrılır (from-space ve to-space).

Yeni nesneler from-space’e yerleştirilir.Canlı nesneler to-space’e kopyalanır, ardından from-space temizlenir.Eski alan (from-space) tamamen boşaltılır. Alanlar yer değiştirir. Bellek sıkıştırılmış olur, hızlı tahsis sağlanır.


## Generational Garbage Collection (Nesil Tabanlı Çöp Toplayıcı):
Bellek “young generation” (genç nesil) ve “old generation” (yaşlı nesil) gibi bölümlere ayrılır.

Yeni nesneler önce genç nesile alınır. İlk GC döngüsünde hayatta kalanlar yaşlı nesle taşınır.

Yaşlı nesilde GC daha az sıklıkla çalışır.

## C#’ta Garbage Collection ile Karşılaştırmalı Özet:
C# ve .NET ortamında Garbage Collector (GC) otomatik olarak bellek yönetimini üstlenir ve Mark-Sweep-Compact algoritması temelli çalışır.

### C# GC’nin Özellikleri:
Üç Nesil (Generation):

- Gen 0: Yeni nesneler.

- Gen 1: Gen 0 GC’den sağ kurtulan nesneler.

- Gen 2: Uzun ömürlü nesneler.

* Dairesel Referanslar Sorun Değildir:

.NET GC, Mark and Sweep algoritması kullandığı için dairesel referanslı nesneleri de tespit eder ve temizler.

* Kök Referanslar:

GC, işlem yığını (stack), statik alanlar ve CPU kayıtları gibi “root”lardan başlayarak nesnelere erişilebilirliği analiz eder.

* Çöp Toplama Anı:

Belirli bir bellek eşik değerine ulaşıldığında veya GC.Collect() ile manuel çağrıldığında tetiklenir.

* Kompaktlama (Compaction):

Bellekteki boşlukları birleştirerek parçalanmayı azaltır. Bu işlem sırasında nesnelerin adresleri değişebilir.

* Zamanlayıcıya Dayalı Olmayan Çalışma:

GC belirli aralıklarla değil, ihtiyaç halinde devreye girer.

* IDisposable Arayüzü ve using Blokları:

Belirli kaynakları (dosya, ağ, bellek dışı kaynaklar) serbest bırakmak için Dispose yöntemi çağrılır. Bu işlemler GC ile değil, manuel yapılır.


# Reference
- https://medium.com/@kasunpdh/garbage-collection-how-its-done-d48135c7fe77