# DotNet Framework

## Fundamentals

- .NET Framework: DI kullanımı üçüncü parti kütüphaneler (Autofac, Unity, Ninject gibi) gerektirir

## Middleware

- HTTP Modules & HTTP Handlers kullanır.
- Middleware zinciri IIS pipeline içinde çalışır.
- Global.asax ve Application_BeginRequest, Application_EndRequest gibi event tabanlı yapılar vardır.
- Özel bir middleware eklemek için DelegatingHandler ya da ActionFilterAttribute gibi mekanizmalar kullanılır.
- HTTP Module: System.Web.IHttpModule kullanarak tüm request-response sürecine müdahale edebiliriz.
- HTTP Handler: System.Web.IHttpHandler ile belli URL pattern’lerine özel işlem yapabiliriz.