# Just-In-Time (JIT) Compiler

    JIT, kodun çalıştırılmadan hemen önce derlenmesini sağlayan bir derleyicidir.
    .NET uygulamalarında Intermediate Language (IL) kodunu Native (Makine) Koduna çevirir.
    Çalışma zamanında derleme yapıldığı için runtime optimizasyonlar mümkündür.

✅ Avantajları:

- Dinamik optimizasyon yapabilir (örneğin, kodun hangi bölümlerinin daha sık çalıştırıldığını analiz ederek optimizasyon yapar).
- Platform bağımsızdır çünkü IL kodu her platformda çalışabilir ve JIT, platforma uygun makine koduna çevirir.
- Geliştirme süreci daha hızlıdır, çünkü her seferinde tüm kodu derlemek gerekmez.

✅ Dezavantajları:

- Başlangıç süresi uzundur, çünkü ilk çalıştırmada IL’den makine koduna çevirme işlemi yapılır.
- Çalışma zamanında ekstra CPU tüketir, çünkü her metot ilk çalıştırıldığında derlenmesi gerekir.

📌 .NET’te Kullanımı:

- .NET Framework ve .NET Core uygulamalarında varsayılan olarak JIT kullanılır.
- JIT, .NET Core ve .NET 5+ sürümlerinde de hala kullanılıyor, ancak bazı durumlarda AOT tercih ediliyor.

# Ahead-Of-Time (AOT) Compiler

    AOT, kodun çalıştırılmadan önce tamamen derlenmesini sağlayan bir derleme modelidir.
    Uygulama çalıştırılmadan önce tüm IL kodu doğrudan makine koduna çevrilir.
    Mobil ve gömülü sistemlerde kullanımı yaygındır.

✅ Avantajları:

- Başlangıç süresi çok daha kısadır, çünkü çalışma zamanında derleme yapılmaz.
- Daha az bellek kullanımı sağlar, çünkü JIT gibi ek bir derleme işlemi olmaz.
- Performans açısından daha stabildir, çünkü tüm kod zaten makine koduna dönüştürülmüştür.

✅ Dezavantajları:

- Derleme süresi uzundur, çünkü tüm kod en baştan derlenmelidir.
- Dinamik optimizasyon yapamaz, çünkü kod önceden derlendiği için çalışma zamanında analiz edemez.
- Platform bağımlıdır, çünkü derlenen kod belirli bir işlemci mimarisi için oluşturulur.

📌 .NET’te Kullanımı:

- amarin, Blazor WebAssembly ve .NET Native projelerinde AOT kullanılır.
- .NET 7 ve .NET 8 ile birlikte "Native AOT" özelliği gelmiştir.
- Gömülü (IoT) ve Mobil cihazlarda AOT daha sık tercih edilir.

# JIT vs AOT Karşılaştırması

| Özellik                      |         JIT (Just-In-Time)         |             AOT (Ahead-Of-Time)              |
| :--------------------------- | :--------------------------------: | :------------------------------------------: |
| Çalışma Zamanında Derleme    |               ✅ Var               |                    ❌ Yok                    |
| İlk Çalıştırma Süresi        |               Yavaş                |                    Hızlı                     |
| Çalışma Zamanı Optimizasyonu |               ✅ Var               |                    ❌ Yok                    |
| Platform Bağımsızlığı        |               ✅ Var               |                    ❌ Yok                    |
| Derleme Süresi               |               Hızlı                |                     Uzun                     |
| Kullanım Alanları            | .NET Framework, .NET Core, ASP.NET | Xamarin, Blazor WebAssembly, IoT, Native AOT |

```mermaid
flowchart TD
    subgraph JIT Derleme Süreci
        A1[C# Kaynak Kodu] --> B1[C# Derleyici]
        B1 --> C1[IL Kodu]
        C1 --> D1[Assembly \n .dll/.exe]
        
        subgraph Çalışma Zamanı
            D1 --> E1[CLR]
            E1 --> F1[JIT Derleyici]
            F1 --> G1[Metod Çağrısı]
            G1 --> |"İlk çağrı"| H1[Metodu JIT ile Derle]
            H1 --> I1[Makine Kodu]
            I1 --> J1[Makine Kodu Önbelleğe Alınır]
            G1 --> |"Sonraki çağrılar"| J1
            J1 --> K1[Kodun Çalıştırılması]
        end
    end

    subgraph AOT Derleme Süreci
        A2[C# Kaynak Kodu] --> B2[C# Derleyici]
        B2 --> C2[IL Kodu]
        C2 --> D2[AOT Derleyici]
        D2 --> E2[Platform Spesifik Makine Kodu]
        E2 --> F2[Yerel Çalıştırılabilir Dosya]
        
        subgraph "Çalışma Zamanı"
            F2 --> G2[İşletim Sistemi]
            G2 --> H2[Doğrudan Çalıştırma]
            H2 --> I2[İşlemci]
        end
    end

    style JIT Derleme Sureci fill:#f0f8ff,stroke:#4682b4
    style AOT Derleme Sureci fill:#f5f5dc,stroke:#8b8b00
    style Çalişma Zamani fill:#e6ffe6,stroke:#006400

```
