## Middleware

- Middleware yapısı tamamen modüler ve bağımsızdır.
- IApplicationBuilder kullanılarak middleware sırası belirlenir.
- app.Use(), app.Run() ve app.Map() gibi metotlar ile middleware’ler zincirlenir.
- IIS’e bağımlı değildir, Kestrel üzerinde çalışabilir.
- Middleware'ler request-response döngüsünü yönetir ve sırayla çalışır.

ASP.NET Core'da middleware pipeline, HTTP isteklerini ve yanıtlarını işleyen bir dizi bileşenden oluşur. Bu bileşenler "soğan modeli" olarak da bilinen bir yapıda çalışır.

### Middleware Türleri

- Use: Pipeline'ı devam ettirebilen middleware
- Run: Pipeline'ı sonlandıran terminal middleware
- Map/MapWhen: Belirli koşullara göre dallanma sağlayan middleware

### Çalışma Akışı

İstek Gelişi: HTTP isteği web sunucusu (genellikle Kestrel) tarafından alınır
İçe Doğru Yolculuk: İstek, pipeline'da tanımlanan sırayla her middleware'den geçer

Her middleware, isteği inceleyebilir veya değiştirebilir
İsteği işledikten sonra, bir sonraki middleware'e aktarabilir veya yanıt döndürerek pipeline'ı sonlandırabilir


Endpoint Çalıştırma: İstek, tüm middleware'lerden geçtikten sonra hedef endpoint'e (controller action, Razor Page, vb.) ulaşır
Dışa Doğru Yolculuk: Yanıt, middleware zincirini tersten (son eklenen middleware'den ilk eklenen middleware'e doğru) geçer

Her middleware, yanıtı inceleyebilir veya değiştirebilir


Yanıt Gönderimi: İşlenen yanıt istemciye geri gönderilir

Çift Yönlü İşleme: Her middleware hem isteği (request) hem de yanıtı (response) işleyebilir
Pipeline Kontrolü: Herhangi bir middleware, next() çağrısını yapmazsa pipeline sonlanır ve yanıt doğrudan istemciye gönderilir
İşlem Sırası:

İstek (Request) yolunda: Pipeline'da tanımlandığı sırada (1 → 2 → 3 → Endpoint)
Yanıt (Response) yolunda: Ters sırada (Endpoint → 3 → 2 → 1)


Exception Handling: Herhangi bir aşamada oluşan istisnalar, dışa doğru yolculuk sırasında yakalanabilir


```Mermaid
sequenceDiagram
    participant Client
    participant Server
    participant MW1 as Middleware 1
    participant MW2 as Middleware 2
    participant MW3 as Middleware 3
    participant Endpoint
    
    Client->>Server: HTTP Request
    Server->>MW1: İsteği ilet
    
    Note over MW1: Request işleme başlangıcı
    MW1->>MW2: next() çağrısı
    
    Note over MW2: Request işleme devam ediyor
    MW2->>MW3: next() çağrısı
    
    Note over MW3: Request işleme devam ediyor
    MW3->>Endpoint: next() çağrısı
    
    Note over Endpoint: Controller/Action çalıştırma
    Endpoint-->>MW3: Response dönüşü
    
    Note over MW3: Response işleme başlangıcı
    MW3-->>MW2: Response dönüşü
    
    Note over MW2: Response işleme devam ediyor
    MW2-->>MW1: Response dönüşü
    
    Note over MW1: Response işleme tamamlanıyor
    MW1-->>Server: Response dönüşü
    Server-->>Client: HTTP Response

```
