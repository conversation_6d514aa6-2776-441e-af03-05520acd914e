## Filter

MVC Filter'ları, ASP.NET Core'da request işleme sürecinin belirli aşamalarında çalışan özel bileşenlerdir. Middleware'lerden farklı olarak, Filter'lar yalnızca MVC pipeline içinde çalışır ve daha spesifik noktalarda müdahale etme imkanı sunar.

### Uygulanma şekilleri

Attribute olarak:
```csharp
[Authorize]
[CustomActionFilter]
public IActionResult Index()
{
    return View();
}

```
Global olarak:
```csharp
csharpCopyservices.AddControllers(options =>
{
    options.Filters.Add(new GlobalActionFilter());
});
```
Controller seviyesinde:
```csharp
csharpCopy[TypeFilter(typeof(CustomResourceFilter))]
public class HomeController : Controller
{
    // ...
}
```

### Çalışma Mantığı

Diagram'da gösterilen Filter türle<PERSON>, çalışma sırasına göre şunlardır:

Authorization Filters:

İlk çalışan filter türüdür
Yetkilendirme işlemlerini gerçekleştirir ([Authorize] attribute)
Yetkilendirme başarısız olursa pipeline'ı sonlandırabilir


Resource Filters:

Authorization'dan hemen sonra çalışır
Tüm MVC filter pipeline'ını kapsayan bir yapıya sahiptir
Caching, resource atama gibi işlemler için kullanılır
OnResourceExecuting ve OnResourceExecuted metotları vardır


Action Filters:

Model binding ve validation işlemlerinden sonra çalışır
Action method çalıştırılmadan önce ve sonra işlem yapabilir
OnActionExecuting ve OnActionExecuted metotları vardır
Loglama, veri dönüşümü gibi işler için kullanılır


Result Filters:

Action method sonucu oluşturulduktan sonra, sonuç çalıştırılmadan önce ve sonra çalışır
OnResultExecuting ve OnResultExecuted metotları vardır
View render etme sürecinde müdahale edebilir


Exception Filters:

Pipeline'da oluşan hataları yakalar
OnException metodu ile hata işleme yapar
Global hata yönetimi için kullanılır

```Mermaid

sequenceDiagram
    participant Client
    participant MW as Middleware Pipeline
    participant RF as Resource Filter
    participant AF as Authorization Filter
    participant MF as Model Binding/Validation
    participant AF2 as Action Filter
    participant A as Action Method
    participant RF2 as Result Filter
    participant E as Exception Filter
    
    Client->>MW: HTTP Request
    MW->>RF: Middleware sonrası
    
    Note over RF: OnResourceExecuting
    RF->>AF: İşlem devam eder
    
    Note over AF: OnAuthorization
    AF->>MF: Yetkilendirme başarılı
    
    Note over MF: Model Binding & Validation
    MF->>AF2: Model hazır
    
    Note over AF2: OnActionExecuting
    AF2->>A: Action çalıştırılabilir
    
    Note over A: Controller Action çalışır
    A-->>AF2: IActionResult döner
    
    Note over AF2: OnActionExecuted
    AF2-->>RF2: Sonuç hazırlanır
    
    Note over RF2: OnResultExecuting
    RF2-->>RF2: View/Result işlenir
    Note over RF2: OnResultExecuted
    
    RF2-->>RF: Sonuç hazırlandı
    
    Note over RF: OnResourceExecuted
    RF-->>MW: Response hazır
    MW-->>Client: HTTP Response
    
    Note over E: Exception olursa
    A-->>E: Hata oluştu
    E-->>MW: Hata işlenir

```