## Fundamentals

- .NET Core, Kestrel web sunucusu sayesinde daha hızlı çalışır
- Server GC modunu kullanarak daha iyi bellek yönetimi sunar.
- Workstation vs Server GC: .NET Core, çok çekirdekli işlemcilerde daha iyi performans veren Server GC’yi kullanır.
- Tiered Compilation: Just-In-Time (JIT) derleyicisi ile kodun en sık kullanılan bölümlerini optimize eden mekanizmalar içerir.
- Span<T> ve Memory<T>: Bu türler sayesinde gereksiz bellek kopyalamalarının önüne geçilir, özellikle büyük veri işlemlerinde avantaj sağlar.
- SDK ve Runtime Ayrımı: .NET Core, runtime ve SDK'yı ayrı şekilde yönetir. Farklı runtime sürümleri bir arada çalışabilir.
- Self-Contained Deployment: .NET Core uygulamalarını kendi runtime'ı ile paketleyerek dağıtabilirsin, - b<PERSON><PERSON><PERSON> hedef makin<PERSON> .NET Core yüklü olmasa bile çalıştırabilirsin.
- NuGet ile Hafifletilmiş Kütüphaneler: .NET Core, sadece gerekli bileşenleri yükleyerek daha küçük uygulama boyutları sağlar.
- DI built-in (gömülü) olarak gelir. IServiceCollection ve IServiceProvider arayüzleri ile bağımlılık yönetimi yapılabilir.
- Cross-platform desteği sayesinde farklı işletim sistemlerinde çalışabilir.
- .NET Core, Thread Pool yönetimini daha verimli kullanır.
- I/O Bound işlemler için async/await pattern’i daha iyi optimize edilmiştir.
- ValueTask<T> ve IAsyncEnumerable<T> gibi yeni async yapıları .NET Core’da daha iyi desteklenir.
- .NET Core, Kestrel sayesinde daha hafif ve yüksek performanslıdır.
- ASP.NET Core middleware yapısı, gereksiz bağımlılıkları azaltarak daha hızlı çalışır.