## Dependency Injection

Dependency Injection is a technique whereby an object (or function) receives its dependencies from an external source rather than creating them internally. This approach promotes loose coupling and enhances testability, making it easier to manage dependencies and swap implementations without modifying the dependent code.

### Types of Dependency Injection

1.Constructor Injection: Dependencies are provided through a class constructor. This is the most common method and is favored for its clarity and simplicity.

```csharp
public class Service
{
private readonly IRepository _repository;
public Service(IRepository repository) { _repository = repository; }
}
````

2.Property Injection: Dependencies are set through public properties. This method is less common and can lead to issues if dependencies are not set before use.

```csharp

public class Service
{
public IRepository Repository { get; set; }
}
````

3. Method Injection: Dependencies are provided as parameters to a method. This method is useful for transient dependencies that are only needed for specific operations.

```csharp
public class Service
{
public void Execute(IRepository repository)
{
// Use repository
}
}

```

### Benefits of Dependency Injection
- Improved Testability : DI makes unit testing easier by allowing developers to inject mock implementations of dependencies. This isolation ensures that tests focus on the functionality of the class being tested without relying on external systems.

- Loose Coupling: By decoupling classes from their dependencies, DI promotes a more modular design. This separation allows for easier maintenance and the ability to swap out implementations without affecting dependent classes.

- Enhanced Maintainability : With a clear structure for managing dependencies, changes to one part of the system have minimal impact on others. This modularity simplifies updates and refactoring.

- Configuration Management : DI frameworks often provide mechanisms for managing configurations, making it easier to handle different environments (development, testing, production) without changing code.




### Dependency Injection Mechanism

.NET Core's built-in Dependency Injection (DI) system is a lightweight, extensible IoC container that's fully integrated into the framework. It provides a clean way to configure and resolve dependencies in your applications.

#### Core Components of .NET Core DI

- Service Registration - Configuring services in the DI container
- Service Resolution - Retrieving instances from the container
- Service Lifetime Management - Controlling how long service instances live

```Mermaid

flowchart TB
    A[Program.cs / Startup.cs] --> B[Service Registration]
    B --> C[Service Collection]
    C --> D[Build Service Provider]
    D --> E[Service Provider]
    E --> F[Service Resolution]
    
    subgraph Registration
    B
    C
    end
    
    subgraph Container
    D
    E
    end
    
    subgraph Usage
    F --> G[Controllers]
    F --> H[Middleware]
    F --> I[Other Services]
    end
    
    style Registration fill:#d0e0ff,stroke:#3080ff
    style Container fill:#ffe0d0,stroke:#ff8030
    style Usage fill:#d0ffe0,stroke:#30ff80

```

#### Service Descriptors
Service descriptors are a fundamental concept in Dependency Injection within .NET. They encapsulate the configuration and lifecycle management of services, providing a flexible and maintainable approach to managing dependencies in applications.

Three most common used service descriptors are:

- Transient : Services are created each time they're requested from the container.
- Scoped : Services are created once per client request (scope).
- Singleton : Services are created once and shared throughout the application's lifetime.

```csharp

// Program.cs in .NET 6+
var builder = WebApplication.CreateBuilder(args);

// Register services
builder.Services.AddTransient<ITransientService, TransientService>();
builder.Services.AddScoped<IScopedService, ScopedService>();
builder.Services.AddSingleton<ISingletonService, SingletonService>();

// Build the service provider
var app = builder.Build();

```

```Mermaid

flowchart TB
    A[Service Lifetimes] --> B[Transient]
    A --> C[Scoped]
    A --> D[Singleton]
    
    B --> B1[New instance each time resolved]
    C --> C1[Same instance within request scope]
    D --> D1[Same instance for entire application]
    
    subgraph Request 1
    E[Controller] --> F[Transient Service #1]
    E --> G[Scoped Service #1]
    E --> H[Singleton Service]
    I[Repository] --> J[Transient Service #2]
    I --> G
    I --> H
    end
    
    subgraph Request 2
    K[Controller] --> L[Transient Service #3]
    K --> M[Scoped Service #2]
    K --> H
    N[Repository] --> O[Transient Service #4]
    N --> M
    N --> H
    end
    
    style Request 1 fill:#d0e0ff,stroke:#3080ff
    style Request 2 fill:#ffe0d0,stroke:#ff8030

```

```Mermaid

classDiagram
    class IServiceCollection {
        +Add(ServiceDescriptor descriptor)
        +AddTransient<TService, TImplementation>()
        +AddScoped<TService, TImplementation>()
        +AddSingleton<TService, TImplementation>()
    }

    class ServiceCollection {
        -List<ServiceDescriptor> _descriptors
        +Add(ServiceDescriptor descriptor)
    }

    class ServiceDescriptor {
        +Type ServiceType
        +Type ImplementationType
        +ServiceLifetime Lifetime
        +object ImplementationInstance
        +Func<IServiceProvider, object> ImplementationFactory
    }

    class IServiceProvider {
        +GetService(Type serviceType)
    }

    class ServiceProvider {
        -ServiceDescriptor[] _descriptors
        +GetService(Type serviceType)
    }

    class ServiceScope {
        +IServiceProvider ServiceProvider
        +Dispose()
    }

    IServiceCollection <|.. ServiceCollection
    ServiceCollection o-- ServiceDescriptor
    ServiceCollection --> ServiceProvider : builds
    IServiceProvider <|.. ServiceProvider
    ServiceProvider o-- ServiceScope

```



#### Examples

1. Delegate or Lambda

```csharp
public class DynamicEmailService
{
    private readonly Func<IEmailService> _emailServiceFactory;

    public DynamicEmailService(Func<IEmailService> emailServiceFactory)
    {
        _emailServiceFactory = emailServiceFactory;
    }

    public void SendDynamicEmail(string recipient, string subject, string body)
    {
        var emailService = _emailServiceFactory();
        emailService.SendEmail(recipient, subject, body);
    }
}

public void ConfigureServices(IServiceCollection services)
{
    services.AddTransient<DevelopmentEmailService>();
    services.AddTransient<ProductionEmailService>();

    services.AddTransient<Func<IEmailService>>(provider =>
    {
        var environment = Configuration["Environment"];
        if (environment == "Development")
        {
            return () => provider.GetService<DevelopmentEmailService>();
        }
        else
        {
            return () => provider.GetService<ProductionEmailService>();
        }
    });

    services.AddTransient<DynamicEmailService>();
}

2. Factory Pattern

```csharp

public interface IEmailService
{
    void SendEmail(string recipient, string subject, string body);
}

public class DevelopmentEmailService : IEmailService
{
    public void SendEmail(string recipient, string subject, string body)
    {
        Console.WriteLine($"Email (dev) sent to {recipient}");
    }
}

public class ProductionEmailService : IEmailService
{
    public void SendEmail(string recipient, string subject, string body)
    {
        Console.WriteLine($"Real email sent to {recipient}.");
        // Gerçek e-posta gönderme işlemi yapılabilir.
    }
}

public interface IEmailServiceFactory
{
    IEmailService CreateEmailService();
}

public class EmailServiceFactory : IEmailServiceFactory
{
    private readonly IConfiguration _configuration;

    public EmailServiceFactory(IConfiguration configuration)
    {
        _configuration = configuration;
    }

    public IEmailService CreateEmailService()
    {
        var environment = _configuration["Environment"];

        if (environment == "Development")
        {
            return new DevelopmentEmailService();
        }

        return new ProductionEmailService();
    }
}

public void ConfigureServices(IServiceCollection services)
{
    services.AddSingleton<IEmailServiceFactory, EmailServiceFactory>();
}

```