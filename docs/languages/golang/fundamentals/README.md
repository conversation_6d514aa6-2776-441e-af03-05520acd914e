# Learn Go

Hey, welcome to the course, and thanks for learning Go. I hope this course provides a great learning experience.

_This course is also available on my [website](https://karanpratapsingh.com/courses/go) and as an ebook on [leanpub](https://leanpub.com/go). Please leave a ⭐ as motivation if this was helpful!_

# Table of contents

- **Getting Started**

  - [What is Go?](#what-is-go)
  - [Why learn Go?](#why-learn-go)
  - [Installation and Setup](#installation-and-setup)

- **Chapter I**

  - [Hello World](#hello-world)
  - [Variables and Data Types](#variables-and-data-types)
  - [String Formatting](#string-formatting)
  - [Flow Control](#flow-control)
  - [Functions](#functions)
  - [Modules](#modules)
  - [Packages](#packages)
  - [Workspaces](#workspaces)
  - [Useful Commands](#useful-commands)
  - [Build](#build)

- **Chapter II**

  - [Pointers](#pointers)
  - [Structs](#structs)
  - [Methods](#methods)
  - [Arrays and Slices](#arrays-and-slices)
  - [Maps](#maps)

- **Chapter III**

  - [Interfaces](#interfaces)
  - [Errors](#errors)
  - [Panic and Recover](#panic-and-recover)
  - [Testing](#testing)
  - [Generics](#generics)

- **Chapter IV**

  - [Concurrency](#concurrency)
  - [Goroutines](#goroutines)
  - [Channels](#channels)
  - [Select](#select)
  - [Sync Package](#sync-package)
  - [Advanced Concurrency Patterns](#advanced-concurrency-patterns)
  - [Context](#context)

- **Appendix**

  - [Next Steps](#next-steps)
  - [References](#references)


  # What is Go?

Go (also known as _Golang_) is a programming language developed at Google in 2007 and open-sourced in 2009.

It focuses on simplicity, reliability, and efficiency. It was designed to combine the efficacy, speed, and safety of a statically typed and compiled language with the ease of programming of a dynamic language to make programming more fun again.

In a way, they wanted to combine the best parts of Python and C++ so that they can build reliable systems that can take advantage of multi-core processors.


# Why learn Go?

Before we start this course, let us talk about why we should learn Go.

## 1. Easy to learn

Go is quite easy to learn and has a supportive and active community.

And being a multipurpose language you can use it for things like backend development, cloud computing, and more recently, data science.

## 2. Fast and Reliable

Which makes it highly suitable for distributed systems. Projects such as Kubernetes and Docker are written in Go.

## 3. Simple yet powerful

Go has just 25 keywords which makes it easy to read, write and maintain. The language itself is concise.

But don't be fooled by the simplicity, Go has several powerful features that we will later learn in the course.

## 4. Career opportunities

Go is growing fast and is being adopted by companies of any size. and with that, comes new high-paying job opportunities.

I hope this made you excited about Go. Let's start this course.

# Installation and Setup

In this tutorial, we will install Go and setup our code editor.

## Download

We can install Go from the [downloads](https://go.dev/dl) section.

## Installation

_These instructions are from the [official website](https://go.dev/doc/install)._

### MacOS

1. Open the package file you downloaded and follow the prompts to install Go.
   The package installs the Go distribution to `/usr/local/go`. The package should put the `/usr/local/go/bin` directory in your `PATH` environment variable.
   You may need to restart any open Terminal sessions for the change to take effect.

2. Add `/usr/local/go/bin` to the PATH environment variable.
   to `~/.zshrc`

```
export PATH=$PATH:/usr/local/go/bin
```   

3. Verify that you've installed Go by opening a command prompt and typing the following command:

```
$ go version
```

4. Confirm that the command prints the installed version of Go.

### Linux

1. Remove any previous Go installation by deleting the `/usr/local/go` folder (if it exists),
   then extract the archive you just downloaded into `/usr/local`, creating a fresh Go tree in `/usr/local/go`:

```
$ rm -rf /usr/local/go && tar -C /usr/local -xzf go1.18.1.linux-amd64.tar.gz
```

_Note: You may need to run the command as root or through sudo._

**Do not untar** the archive into an existing `/usr/local/go` tree. This is known to produce broken Go installations.

2. Add `/usr/local/go/bin` to the PATH environment variable.
   You can do this by adding the following line to your `$HOME/.profile` or `/etc/profile` (for a system-wide installation):

```
export PATH=$PATH:/usr/local/go/bin
```

_Note: Changes made to a profile file may not apply until the next time you log into your computer. To apply the changes immediately, just run the shell commands directly or execute them from the profile using a command such as source `$HOME/.profile`._

3. Verify that you've installed Go by opening a command prompt and typing the following command:

```
$ go version
```

4. Confirm that the command prints the installed version of Go.


# Structure of a Go program

Let's write our first hello world program, we can start by initializing a module. For that, we can use the `go mod` command.

```bash
$ go mod init example
```

But wait...what's a `module`? Don't worry we will discuss that soon! But for now, assume that the module is basically a collection of Go packages.

Moving ahead, let's now create a `main.go` file and write a program that simply prints hello world.

```go
package main

import "fmt"

func main() {
	fmt.Println("Hello World!")
}
```

_If you're wondering, `fmt` is part of the Go standard library which is a set of core packages provided by the language._

Now, let's quickly break down what we did here, or rather the structure of a Go program.

First, we defined a package such as `main`.

```go
package main
```

Then, we have some imports.

```go
import "fmt"
```

Last but not least, is our `main` function which acts as an entry point for our application, just like in other languages like C, Java, or C#.

```go
func main() {
	...
}
```

Remember, the goal here is to keep a mental note, and later in the course, we'll learn about `functions`, `imports`, and other things in detail!

Finally, to run our code, we can simply use `go run` command.

```bash
$ go run main.go
Hello World!
```

Congratulations, you just wrote your first Go program!

## Executable ve Library Topics

If it is a executable application, `main` is in it.
The function is possible and it is an **Executable**.

If this is a **Library**, you can use the various code collections in it.
It consists of functions.

In our example, we have a **Library** containing many functions in the `fmt` package.
So it is a library.


## `init` Function

All source code (*source - each file containing golang code*) files have their own
It could be an `init` function. During compilation, respectively;

- All variable definitions
- Initializing defined variables
- Commissioning and initializing all imported packages

After the work is done, the 'init' function is called in that source code file and
then the `main` function is triggered:

```go
package main

import "fmt"

func main() {
	fmt.Println("hello from main!")
}

func init(){
	fmt.Println("init called!")
}
```

---

## Package Scope

Nothing is **global**, whether variable, constant or function.
Whatever comes to your mind, either in the `main` package or in another package
lives. Therefore, either in package scope (*scope*) or in function
is within the scope of

```go
package main

import "fmt"

func main() {
	fmt.Println("Merhaba")
}
```

The `println` function is actually in the `fmt` package and can be exported via `import "fmt"`.
from the world (*more precisely, from the standard library*) into our application.
taken. Therefore, within **package scope**, Package Scope is a
is the function.

Since the first letter of `Println`, `P`, is uppercase, this means that the function is external to that function.
It tells us that it is open to the world, that is, **Exportable**.

In this regard, by **importing** the `Println` function from the `fmt` package
We will call.

## Go CLI

### `go run FILE.go`

It runs the file you provide. First it compiles (*compile*), then it outputs
It throws it to a hidden location in the operating system, and then extracts the compiled binary from there.
It runs by calling.

### `go run .`

For it to work this way, there must be a go module in the middle, otherwise;

```bash
$ go run .
go: go.mod file not found in current directory or any parent directory; see 'go help modules'
```


```bash
$ go mod init github.com/vigo/deneme
go: creating new go.mod: module github.com/vigo/deneme
go: to add module requirements and sums:
	go mod tidy

$ go run .
Hello World
```

### `go build`

It compiles in your current directory and creates the executable binary.

### `go install`

After compiling the resulting executable binary `${GOPATH}/bin`
throws it under.

---

## Unicode

Golang, otomatik olarak `UTF-8` karakter seti kullanıyor. Bu şu anlama
geliyor; kod içerisinde Türkçe harf taşıyan değişken ya da sabit
tanımlayabilirsiniz:

```go
package main

import "fmt"

func main() {
	kullanıcıAdı := "vigo"
	fmt.Println(kullanıcıAdı) // vigo
}
```

Bu sadece **proof-of-concept**, sadece bunun doğru (*valid*) bir kod olduğunu
unutmamakla beraber, bu tür deklerasyonlar kullanılmasını pek **tavsiye
etmiyoruz** :)

Unicode kapsamındaki tüm karakterler ve `_` karakteri golang için bir
**KARAKTER** tanımı. `0`’dan `9`’a kadar sayılar, Octal yani 8’lik sayı
sistemi için `0`’dan `7`’ye sayılar, Hexadecimal yani 16’lık sayı sistemi için
`0`’dan `9`’a kadar sayılar ve `A`’dan `F`’e kadar harfler kullanılıyor.

---

## Anahtar Kelimeler

Toplamda **25** tane anahtar kelime bulunur:

    break        default      func         interface    select
    case         defer        go           map          struct
    chan         else         goto         package      switch
    const        fallthrough  if           range        type
    continue     for          import       return       var

- Sabitler (*constants*): `true`, `false`, `iota`, `nil`
- Tipler (*types*): `int`, `int8`, `int16`, `int32`, `int64`, `uint`, `uint8`,
  `uint16`, `uint32`, `uint64`, `uintptr`, `float32`, `float64`, `complex64`, 
  `complex128`, `bool`, `byte`, `rune`, `string`, `error`
- Fonksiyonlar: `make`, `len`, `new`, `append`, `copy`, `close`, `delete`, 
  `complex`, `real`, `imag`, `panic`, `recover`

---

## Operatörler

    +    &     +=    &=     &&    ==    !=    (    )
    -    |     -=    |=     ||    <     <=    [    ]
    *    ^     *=    ^=     <-    >     >=    {    }
    /    <<    /=    <<=    ++    =     :=    ,    ;
    %    >>    %=    >>=    --    !     ...   .    :
         &^          &^=

şeklindedir.

Ek olarak; hafıza adresi için `&identifier`, işaret (*point*) ettiği değer
için `*identifier` ve **channels** için `<-` işaretleri kullanılır.

---

## İşaretçiler (*Identifiers*)

Değişken, sabit yani tip tanımlarken işaretçi yani **identifier** olarak
aşağıdaki gibi tanımlamalar yapmak mümkün:

```go
a 
_x1
BuExportEdilebilir
uğur
```

Sayısal tanımlamalarda da;

```go
42        // 10’luk sayı
0600      // 8’lik sayı
0xFF      // 16’lık sayı
0.        // Kesirli ondalıklı
1.2       // Kesirli ondalıklı
072.40    // == 72.40
1.e+0
011i      // == 11i
170141183460469231731687303715884105727 // çılgın
```

kullanılabiliyor. Ek olarak **Rune Literal** yani Unicode karakteri ifade eden
bir yöntem de mevcut:

```go
'a'
'ä'
'本'
'\t'
'\000'
'\007'
'\377'
'\x07'
'\xff'
'\u12e4'
'\U00101234'
```

---

## Gömülü Veri Tipleri (*Data Types*)

Golang standart kütüphanesi bir kısım hazır veri tipiyle birlikte geliyor.
Bunlar;

- Strings: Metinsel tipler
- Booleans: `true` / `false` mantıksal veri tipleri
- Numerics: Sayısal tipler `int`, `float32`, `complex` gibi...
- Composite (Unnamed) Types: Arrays, Slices, Structs ve Maps gibi...

---

## Kod Stili

2 tür yorum satırı (*Comment*) bulunuyor:

1. *Line comment* : `// bu bir yorum satırı` şeklinde tek satır
1. *General comment / block comment* : `/* bu çok satır oluyor */` şeklinde çoklu satır.

Kod satırını tanımlamak için bazı dillerdeki gibi `;` yazılmıyor. Örneğin `C`
ya da `JavaScript`, `PHP` gibi diller kod satırının bittiğini sona eklenen
`;` karakterinden anlarken golang bunu kendisi yönetiyor ve satır sonlarına
`;` yazmaya gerek kalmıyor. Eğer karmaşık bir ifade yazıyorsanız `;` aşağıdaki
gibi kullanılıyor:

```go
// short-if declaration - inner-scope
if v := math.Pow(x, n); v < lim {
	return v
}

for i := 5; i< 9; i++  {
  fmt.Println(i)
}
```



# Variables

Let's start with declaring a variable.

İçinde değer saklayan depo alanına değişken denir. Aslında veriye ulaşmak için
kullanılan referanstır. Golang değişkenin tipine göre içindeki değeri anlar. 2
tür değişken tanımlama şekli var:

1. `var` anahtar kelimesi kullanarak uzun değişken tanımlama
   (*long variable declaration*)
2. `:=` kullanarak tanımlama. Bu yöntem kısa değişken tanımlama 
   (*short variable declaration*) olarak bilinir.


This is also known as declaration without initialization:

```go
var foo string
```

Declaration with initialization:

```go
var foo string = "Go is awesome"
```

Multiple declarations:

```go
var foo, bar string = "Hello", "World"
// OR
var (
	foo string = "Hello"
	bar string  = "World"
)
```

Type is omitted but will be inferred:

```go
var foo = "What's my type?"
```

Shorthand declaration, here we omit `var` keyword and type is always implicit. This is how we will see variables being declared most of the time. We also use the `:=` for declaration plus assignment.

```go
foo := "Shorthand!"
```

_Note: Shorthand only works inside `function` bodies._

Eğer tip vermezsek golang **type inference** yapıyor ve bunun bir integer
olduğunu düşünüyor. `var z int` durumunda ise, `z`’nin tipinin **integer**
olduğunu biliyor ve değişkeni ilklendirirken (*initialize*) ön tanımlı olan
değeri yani **zero-value**’sunu yani `0`’ı atıyor. Peki `var q string` olsaydı
değeri ne olacaktı? Tabiki boş string. Yani `""`.

Baz tiplerin **zero-value** değerleri;

```go
var a int                // 0
var b float32            // 0
var c complex64          // (0+0i)
var d string             // 
var e bool               // false
var f byte               // 0
var g []int              // [] bu içinde integerların olabileceği bir array
var h struct{}           // {}
var i map[string]string  // map[]
```

Özetle numerik tipler için `0`, boolean için `false` ve string için `""`
varsayılan değerlerdir. 



```go
package main

import (
	"fmt"
	"unsafe"  // bu kütüphane sayesinde `Sizeof` kullanıyoruz, sonuç byte cinsinden
)

func main() {
	var a int       // a’yı tanımladık
	var b float32
	var c string

	fmt.Printf("%v bytes\n", unsafe.Sizeof(a)) // 8 bytes
	fmt.Printf("%v bytes\n", unsafe.Sizeof(b)) // 4 bytes
	fmt.Printf("%v bytes\n", unsafe.Sizeof(c)) // 16 bytes

	a = 1000000000000000000 // tanımladığımız a’yı kullandık, değerini değiştirdik.
	fmt.Printf("%v bytes\n", unsafe.Sizeof(a)) // 8 bytes
}
```

Dikkat etmeniz gereken şey; `var a int` ile tanımladıktan sonra artık `a`
değişkeninin değerini değiştirmek için sadece `a = DEĞER` şeklinde ilerliyoruz.
Keza `a` tip olarak numerik yani `int` olduğu için artık `a`’nın değeri başka
bir tip olamıyor. Yani aşağıdaki örnek çalışmaz;

```go
package main

import "fmt"

func main() {
	var a int

	a = "Helllo"

	fmt.Printf("%v\n", a)
}
```

Kısa değişken tanımlamanın bazı kısıtları var;

- Sadece **fonksiyon** içinde çalışıyor
- Tekli atamalarda 2 kere tekrar edilemiyor
- Çoklu atamalarda tekrar oluyor ama her seferinde değeri değişiyor
- Kapsama göre tekrar olabiliyor

Örnekler hem `main` fonksiyonu içinde olduğu için sıkıntı çıkmıyor ama aşağıdaki
gibi bir yazım olamıyor, zaten bu kod derlenmiyor bile;

```go
// Sadece fonksiyon içinde çalışıyor
package main

import "fmt"

a := 5

func main() {
	fmt.Printf("a: %v\n", a)
}

// non-declaration statement outside function body
```

Çoklu atamalardaki kısıtlılık durumu;

```go
// Çoklu atamalarda tekrar oluyor ama her seferinde değeri değişiyor
package main

import "fmt"

func main() {
	number1, number2 := example1()
	fmt.Printf("number1: %d , number2: %d\n", number1, number2) // number1: 1 , number2: 2

	number1, number3 := example2()
	fmt.Printf("number1: %d , number3: %d\n", number1, number3) // number1: 100 , number3: 200

	number4, number1 := example1()
	fmt.Printf("number4: %d , number1: %d\n", number4, number1) // number4: 1 , number1: 2
}

func example1() (int, int) {
	return 1, 2
}

func example2() (int, int) {
	return 100, 200
}
```

Kapsam durumuna göre (*scope*);

```go
// Kapsama göre tekrar olabiliyor
package main

import "fmt"

var number int = 999

func main() {
	fmt.Printf("number from main: %d\n", number) // number from main: 999
	example1()
	fmt.Printf("number from main after example1: %d\n", number) // number from main after example1: 999
}

func example1() {
	number := 1 // inner-scope
	fmt.Printf("example1 number: %d\n", number) // example1 number: 1

	number = 666 // inner-scope
	fmt.Printf("example1 number: %d\n", number) // example1 number: 666
}
```

İleriki konularda `if`, `for`, `switch` ifadelerinde bu kısa tanımlamanın özel
kullanımlarını da göreceğiz ama hızlı bir örnek;

```go
package main

import "fmt"

func main() {
	number := 100
	fmt.Printf("number is: %d\n", number) // number is: 100
	if number := example1(); number == 1 {
		// number's scope is inner now!
		fmt.Printf("Hello World! and number is: %d\n", number) // Hello World! and number is: 1
	}
	fmt.Printf("number is still: %d\n", number) // number is still: 100
}

func example1() int {
	return 1
}
```

Diğer çoklu tanımlama/atama şekilleri de yapmak mümkün;

```go
package main

import "fmt"

var a, b int                        // a ve b int tipinde
var (
	x       = 1                    // x dinamik tip 1
	y       = 2                    // y dinamik tip 2
	abc int = 99                   // abc statik tip, int 99
)

func main() {
	a = 5
	b = 10

	fmt.Printf("a: %v\n", a)       // 5
	fmt.Printf("b: %v\n", b)       // 10

	fmt.Printf("x: %v\n", x)       // 1
	fmt.Printf("y: %v\n", y)       // 2

	fmt.Printf("abc: %v\n", abc)   // 99

	num1, num2 := 101, 201          // num1’e 101, num2’ye 201
	fmt.Printf("num1: %v\n", num1)
	fmt.Printf("num2: %v\n", num2)

}
```

Değişken isimlendirmesinde dikkat edeceğimiz kurallar;

1. Mutlaka harf ile başlamalı
1. İçinde harf, sayı ve `_` (*underscore*) olabilir ama olmasa iyi olur
1. `camelCase`, `BumpyCaps`, `mixedCase` şeklinde tanımlama yapılabilir
1. Anlaşılır olmalıdır

Örneğin veritabanından gelen kayıtların sayısı için bir değişken tanımlamak
gerekese; "NUMBER OF RECORDS" ya da "LENGTH OF RECORDS" ya da "RECORDS LENGTH"
kafamızda olsa;

```go
var lengthOfRecords int // ya da
var recordsLength       // ya da
var numRecs             // çok tercih edilmememli
var recordsAmount       //
```

gibi varyasyonlar olabilir. Eğer imkan varsa tek bir kelime ile ifade etmek en
iyi yöntemdir. Tüm bu kurallar tüm identifier’lar için geçerlidir. 

Nerede bir değişken kullanımı görürseniz mutlaka o değişkenin değerini yani
**Value of**’unu kullandığınızı **unutmayın**!

---

## Değişkenleri Gölgeleme

```go
// short-if inner-scope durumu
package main

import "fmt"

func main() {
	n, err := fmt.Println("Hello, playground") // err declared but not used
	if _, err := fmt.Println(n); err != nil {
		fmt.Println(err)
	}
}
```

`if` bloğu içindeki `err` kapsam (*scope*) olarak işlendi bitti. Baştaki `err`
ise deklare edildi ama kullanılmadı... Ancak aşağıdaki gibi olsa
derlenebilirdi:

```go
package main

import "fmt"

func main() {
	n, err := fmt.Println("Hello, playground")
	if _, err := fmt.Println(n); err != nil {
		fmt.Println(err)
	}
	fmt.Println(err)
}

// Hello, playground
// 18
// <nil>
```

Tanım gölgeleme (*Declaration Shadowing*) örneği:

```go
package main

import (
	"fmt"
	"os"
)

func otherFunc(n int, buf []byte) {
	fmt.Println(n, buf)
}

// BadRead is an example for shor declaration shadowing
func BadRead(f *os.File, buf []byte) error {
	var err error // zero-value nil

	for {
		n, err := f.Read(buf) // shadows 'err' above
		if err != nil {
			break // causes return of WRONG value
		}
		otherFunc(n, buf)
	}
	return err // will always be nil, returns var err error line
}

func main() {
	fmt.Println("Hello World")
}
```





## Constants

We can also declare constants with the `const` keyword. Which as the name suggests, are fixed values that cannot be reassigned.

```go
const constant = "This is a constant"
```

It is also important to note that, only constants can be assigned to other constants.

```go
const a = 10
const b = a // ✅ Works

var a = 10
const b = a // ❌ a (variable of type int) is not constant (InvalidConstInit)
```

Sabit olarak tanımlanabilir tipler;

- Boolean: `bool`
- Rune: `rune` (*aslında `int32` için takma ad*)
- Integer: `int` familyası
- Floating-point: `float` familyası
- Complex: `complex` familyası
- String: `string`

şeklindedir. Genel olarak Rune, Integer, Floating-point ve Complex tipinden
olan sabitlere **Numeric Constants** denir.

Sabiti tanımlarken `const` anahtar kelimesi kullanılır, takiben işaretçisi
yani **identifier**’ı, tipi ve son olarak değeri atanır ve bu tür tanıma
**Typed Constant** denir;

```go
const domain string = "example.com"
const pi float32 = 3.14
```

Peki **Untyped Constant** nedir? Yani tipi tanımlanmamış? Eğer tanımlama
esnasında tip belirmemişsek golang derleyicisi bu tipi tahmin ederek, gerekli
işlemi yapar. Bu konu **Type Inference** olarak bilinir. **Inference** anlam
çıkarmak, netice çıkarmak demektir;

```go
const a = 5          // untyped integer constant
const b = "vigo"     // untyped string constant
const pi = 3.14      // untyped floating-point constant
const foo = '1'      // 49  - untyped rune constant
const world = "世界"  // untyped unicode string constant
```

Hatta `fmt.Printf("%v\n", len("Hello")) // 5` ifadesinde `len` ile `"Hello"`
string’inin boyunu alırken aslında `"Hello"` bir **untyped string constant**’dır.
Ya da `fmt.Printf("%v\n", 1 > 2) // false` bu ifadedeki `1` ve `2` de
**untyped integer constant**’dır.

---

### iota

`iota` sadece `constant`’lar için geçerli olup küçük parça anlamıdır. Orijini
Yunan alfabesindeki 9. karakterdir.

https://en.wikipedia.org/wiki/Iota

Belli bir mantıkta otomatik sabit değeri üretmek için kullanılır.

```go
package main

import "fmt"

type Num int

const (
	Sıfır Num = iota * 2
	İki
	Dört
	Altı
	Sekiz
)

func main() {
	fmt.Println("Sıfır", Sıfır)
	fmt.Println("İki", İki)
	fmt.Println("Dört", Dört)
	fmt.Println("Altı", Altı)
	fmt.Println("Sekiz", Sekiz)
}
// Sıfır 0
// İki 2
// Dört 4
// Altı 6
// Sekiz 8
```



## Data Types

Perfect! Now let's look at some basic data types available in Go. Starting with string.

### String

In Go, a string is a sequence of bytes. They are declared either using double quotes or backticks which can span multiple lines.

```go
var name string = "My name is Go"

var bio string = `I am statically typed.
									I was designed at Google.`
```

İçinde Unicode karakterler bulunan karakterler dizisidir. Tip tanımlaması
yaparken `string` anahtar kelimesi ile ifade edilir. Önceki bölümlerde bahsettiğim
gibi ya çift tırnak `""` ya da back-tick <code>\`</code> içinde kullanılır.

```go
package main

import "fmt"

var message1 = "Hello World 1"
var message2 string

func main() {
	message2 = "Hello World 2"
	message3 := "Hello World 3"

	fmt.Println(message1)
	fmt.Println(message2)
	fmt.Println(message3)
}
```

Unicode desteği olduğu için aşağıdaki gibi kod çalışır;

```go
message := "Hava 42\u00B0 derece!"   // \u00B0 = °
fmt.Println(message)                 // Hava 42° derece!
```

- `uint8` -> `byte` anlamında
- `int32` -> `rune` anlamında

aynı şeydir. Bu bağlamda `string` de **immutable** yani değeri değiştirilemez
karakterler serisidir. Fiziksel olarak UTF-8 byte dizisi ama içeride `rune`
lardan oluşur:

```go
package main

import "fmt"

func main() {
	s := "uğur" // ğ önemli
	fmt.Printf("%10T %[1]v\n", s)
	fmt.Printf("%10T %[1]v\n", []rune(s))
	fmt.Printf("%10T %[1]v\n", []byte(s))
}
//  string uğur
// []int32 [117 287 117 114]     -> rune, 4 karater
// []uint8 [117 196 159 117 114] -> byte, 5 karater, ğ çünkü 2 karakter
```

Değiştirilemezden kasıt;

```go
package main

import "fmt"

func main() {
	s := "hello vigo"
	fmt.Println(s[1]) // 101, byte cinsi

	// s[1] = "x" // cannot assign to s[1] (strings are immutable)
}
```

Neticede bir koleksiyon olduğu için indeks numarası ile (*Slice konusunda göreceğiz*)
içindeki karakterlere erişilebilir:

```go
package main

import "fmt"

func main() {
	message := "Hava 42\u00B0 derece!"

	for index := range message {
		// %c : karakter
		// %d : digit/sayı
		// %x : hexadecimal/16'lık sistem
		fmt.Printf("%c | %d | $%x\n", message[index], message[index], message[index])
	}
}
// H | 72 | $48
// a | 97 | $61
// v | 118 | $76
// a | 97 | $61
//   | 32 | $20
// 4 | 52 | $34
// 2 | 50 | $32
// Â | 194 | $c2
//   | 32 | $20
// d | 100 | $64
// e | 101 | $65
// r | 114 | $72
// e | 101 | $65
// c | 99 | $63
// e | 101 | $65
// ! | 33 | $21
```

**Slicing** yani `start` ve `end` indeksi ile kesme/biçme yapabiliriz.
Başlangıç indeksi `0`’dır:

```go
package main

import "fmt"

func main() {
	s := "hello world"
	//    0123456789x

	fmt.Println(s)        // hello world
	fmt.Println(s[:5])    // hello - 0'dan 5'e kadar, 5 hariç
	fmt.Println(s[6:])    // world - 6'dan sona kadar
	fmt.Println(s[2:5])   // llo   - 2'den 5'e kadar, 5 hariç

//	fmt.Println(s[:-1]) invalid slice index -1 (index must be non-negative)
}
```

**String Concatenation** yani metinleri birbirleriyle toplamak da mümkün;

```go
package main

import "fmt"

func main() {
	hello := "Hello"
	world := "World"
	
	message := hello + " " + world
	fmt.Printf("%s\n", message) // Hello World
}
```

Not: Format String konusunda `fmt` paketini detaylı işleyeceğiz ve daha güzel
yöntemler göreceğiz.

---

#### String Kalıpları (*String Literals*)

İki tür **String Literal** yani metinsel ifade yöntemi var;

1. **Raw String**: Back-tick içinde yani **\`** karakterleri içinde kullanılan.
1. **Interpreted String**: Çift tırnak içinde kullanılan.

**Raw String** içindeki back-slash yani `\` işlenmez. Zaten **Raw** işlenmemiş
anlamındadır. **Interpreted** yani yorumlanmış olanlarda ise tam tersi
geçerlidir:

```go
package main

import "fmt"

func main() {
	
	rawString := `Hello\nWorld`
	interpretedString := "Hello\nWorld"
	
	fmt.Println(rawString) // Hello\nWorld
	fmt.Println(interpretedString) // Hello
	                               // World
}
```

Örnekler;

```go
`abc`                // "abc"
`\n
\n`                  // "\\n\n\\n"
"\n"
"\""                 // `"`
"Hello, world!\n"
"日本語"
"\u65e5本\U00008a9e"

// hepsi aynı çıktıyı verir...
"日本語"                                 // Unicode text girdi, çıktı: 日本語
`日本語`                                 // Unicode raw girdi, çıktı: 日本語
"\u65e5\u672c\u8a9e"                    // Unicode girdi, çıktı: 日本語
"\U000065e5\U0000672c\U00008a9e"        // Unicode girdi, çıktı: 日本語
"\xe6\x97\xa5\xe6\x9c\xac\xe8\xaa\x9e"  // bytes girdi, çıktı: 日本語
```





### Bool

Next is `bool` which is used to store boolean values. It can have two possible values - `true` or `false`.

```go
var value bool = false
var isItTrue bool = true
```

**Operators**

We can use the following operators on boolean types

| Type     | Syntax          |
| -------- | --------------- |
| Logical  | `&&` `\|\|` `!` |
| Equality | `==` `!=`       |


`true` ve `false` değerleri için kullanılır. `1 bit integer` olarak ifade edilir.

```go
package main

import "fmt"

func main() {
	var result bool
	
	fmt.Printf("%t\n", result) // false, initial value
	
	if 2 > 1 {
		result = true // evet, 2 büyüktür 1
	}

	fmt.Printf("%v\n", result) // value’su: true
	fmt.Printf("%t\n", result) // boolean olarak value’su: true
}
```

Özetle `true` ve `false` aslında birer sabittir. Mantıksal karşılaştırma yapmak
için `&&` ve `||` kullanılır. Eşitlik için `==`, eşit değildir için `!=`, olumsuzluk
yani **NOT** için `!` kullanılır;

```go
package main

import "fmt"

func main() {
	fmt.Printf("true && true -> %t\n", true && true)
	fmt.Printf("true && false -> %t\n", true && false)
	fmt.Printf("false && false -> %t\n", false && false)
	fmt.Printf("false && true -> %t\n", false && true)

	fmt.Printf("true || true -> %t\n", true || true)
	fmt.Printf("true || false -> %t\n", true || false)
	fmt.Printf("false || false -> %t\n", false || false)
	fmt.Printf("false || true -> %t\n", false || true)

	fmt.Printf("!true -> %t\n", !true)
	fmt.Printf("!false -> %t\n", !false)
}
```



### Numeric types

Now, let's talk about numeric types.

**Signed and Unsigned integers**

Go has several built-in integer types of varying sizes for storing signed and unsigned integers

The size of the generic `int` and `uint` types are platform-dependent. This means it is 32-bits wide on a 32-bit system and 64-bits wide on a 64-bit system.

```go
var i int = 404                     // Platform dependent
var i8 int8 = 127                   // -128 to 127
var i16 int16 = 32767               // -2^15 to 2^15 - 1
var i32 int32 = -2147483647         // -2^31 to 2^31 - 1
var i64 int64 = 9223372036854775807 // -2^63 to 2^63 - 1
```

Similar to signed integers, we have unsigned integers.

```go
var ui uint = 404                     // Platform dependent
var ui8 uint8 = 255                   // 0 to 255
var ui16 uint16 = 65535               // 0 to 2^16
var ui32 uint32 = 2147483647          // 0 to 2^32
var ui64 uint64 = 9223372036854775807 // 0 to 2^64
var uiptr uintptr                     // Integer representation of a memory address
```

If you noticed, there's also an unsigned integer pointer `uintptr` type, which is an integer representation of a memory address. It is not recommended to use this, so we don't have to worry about it.

**So which one should we use?**

It is recommended that whenever we need an integer value, we should just use `int` unless we have a specific reason to use a sized or unsigned integer type.

**Byte and Rune**

Golang has two additional integer types called `byte` and `rune` that are aliases for `uint8` and `int32` data types respectively.

```go
type byte = uint8
type rune = int32
```

_A `rune` represents a unicode code point._

```go
var b byte = 'a'
var r rune = '🍕'
```

**Floating point**

Next, we have floating point types which are used to store numbers with a decimal component.

Go has two floating point types `float32` and `float64`. Both type follows the IEEE-754 standard.

_The default type for floating point values is float64._

```go
var f32 float32 = 1.7812 // IEEE-754 32-bit
var f64 float64 = 3.1415 // IEEE-754 64-bit
```

**Operators**

Go provides several operators for performing operations on numeric types.

| Type                | Syntax                                                   |
| ------------------- | -------------------------------------------------------- |
| Arithmetic          | `+` `-` `*` `/` `%`                                      |
| Comparison          | `==` `!=` `<` `>` `<=` `>=`                              |
| Bitwise             | `&` `\|` `^` `<<` `>>`                                   |
| Increment/Decrement | `++` `--`                                                |
| Assignment          | `=` `+=` `-=` `*=` `/=` `%=` `<<=` `>>=` `&=` `\|=` `^=` |

**Complex**

There are 2 complex types in Go, `complex128` where both real and imaginary parts are `float64` and `complex64` where real and imaginary parts are `float32`.

We can define complex numbers either using the built-in complex function or as literals.

```go
var c1 complex128 = complex(10, 1)
var c2 complex64 = 12 + 4i
```

Gündelik hayatta kullandığımız tam sayılar yani **Integers** ve ondalıklı
sayılar yani **Floats** olmak üzere iki ana grup mevcut. Sayının kaç bitlik
alan kapladığını sonundaki ekten anlayabiliriz. `int8` 8-bit, `int64` 64-bit
anlamındadır. 

Başına eklenen `u` o sayının **unsigned integer** yani sadece pozitif tam sayı
olabileceğini söyler bize.

| Tip          | Açıklama                                                                                                 |
|:-------------|:---------------------------------------------------------------------------------------------------------|
| `int8`       | `-128` ile `127` arasında değer taşır.                                                                   |
| `int16`      | `-32768` ile `32767` arasında değer taşır.                                                               |
| `int32`      | `-2147483648` ile `2147483647` arasında değer taşır.                                                     |
| `int64`      | `-9223372036854775808` ile `9223372036854775807` arasında değer taşır.                                   |
| `uint8`      | `0` ile `255` arasında değer taşır.                                                                      |
| `uint16`     | `0` ile `65535` arasında değer taşır.                                                                    |
| `uint32`     | `0` ile `4294967295` arasında değer taşır.                                                               |
| `uint64`     | `0` ile `18446744073709551615` arasında değer taşır.                                                     |
| `float32`    | 32-bit ondalık sayılar, `-3.4E+38` ile `+3.4E+38` arası                                                  |
| `float64`    | 64-bit ondalık sayılar, `-1.7E+308` ile `+1.7E+308` arası                                                |
| `complex64`  | `float32` tipinde gerçek sayı ve hayali sayı: `1.0 + 7i`                                                 |
| `complex128` | `float64` tipinde gerçek sayı ve hayali sayı: `1.0 + 7i`                                                 |
| `byte`       | `uint8` için takma ad                                                                                    |
| `rune`       | Karater ifade etmek için `int32`’ye takma ad                                                             |
| `int`        | En az 32-bit’lik (*64-bit de olabilir*) negatif/pozitif sayı ifade etmek için. Dikkat! bu `int32` değil! |
| `uint`       | En az 32-bit’lik (*64-bit de olabilir*) pozitif sayı ifade etmek için. Dikkat! bu `uint32` değil!        |
| `uintptr`    | Hafıza adres işaretçilerini saklamak için (*memory address pointers*)                                    |

`complex64` ve `complex128` tipleri aslında tümleşik gelen `complex` fonksiyonu
ile bu tür sayıları üretir. Bu fonksiyonun imzasına baktığımızda;

```go
func complex(r, i FloatType) ComplexType
```

2 tane `r` ve `i` değişkenine atanmış `FloatType` tipinde girdi alıp geriye
`ComplexType` döner:

```go
package main

import "fmt"

func main() {
	c1 := complex(5, 7) // ister fonksiyon ile
	c2 := 1 + 3i        // ister direkt yazarak

	fmt.Printf("%v\n", c1) // (5+7i)
	fmt.Printf("%v\n", c2) // (1+3i)
}
```

Golang tanımlanan değişkenlerin tipi konusunda çok katıdır. Yani numerik
olduğunu düşündüğünüz iki sayıyı kafanıza göre işleyemezsiniz. `int` tipindeki
bir sayı ile `float32` tipindeki sayıyı toplamak için tip dönüştürmesi (*Type
Conversion*) yapmak gerekiyor ve bu iki farklı türün toplamının hangi tipte
sonuç vermesi gerekiyorsa o türden işlem yapmak gerekiyor:

```go
package main

import "fmt"

func main() {
	a := 32
	fmt.Printf("a: %[1]v (%[1]T)\n", a) // argument reuse tekniği!

	b := 1.1
	fmt.Printf("b: %[1]v (%[1]T)\n", b)
	
	sum1 := a + int(b)
	fmt.Printf("sum1: %[1]v (%[1]T)\n", sum1)

	sum2 := float64(a) + b
	fmt.Printf("sum2: %[1]v (%[1]T)\n", sum2)
}
```


## Zero Values

Now let's discuss zero values. So in Go, any variable declared without an explicit initial value is given its _zero value_. For example, let's declare some variables and see:

```go
var i int
var f float64
var b bool
var s string

fmt.Printf("%v %v %v %q\n", i, f, b, s)
```

```bash
$ go run main.go
0 0 false ""
```

So, as we can see `int` and `float` are assigned as 0, `bool` as false, and `string` as an empty string. This is quite different from how other languages do it. For example, most languages initialize unassigned variables as null or undefined.

This is great, but what are those percent symbols in our `Printf` function? As you've already guessed, they are used for formatting and we will learn about them later.


## Type Conversion

Moving on, now that we have seen how data types work, let's see how to do type conversion.

```go
i := 42
f := float64(i)
u := uint(f)

fmt.Printf("%T %T", f, u)
```

```bash
$ go run main.go
float64 uint
```

And as we can see, it prints the type as `float64` and `uint`.

_Note that this is different from parsing._

Tipleri birbirine dönüştürmek anlamındadır. Built-in ya da custom type’ler
aynı bir fonksiyon çağırır gibi tip dönüştürmesi için kullanılır:

```go
var i int = 42
var f float64 = float64(i)
var u uint = uint(f)

// alternative syntax
i := 42
f := float64(i)
u := uint(f)
```

---

### Ascii to Int

```go
package main

import (
	"fmt"
	"strconv"
)

func main() {
	v := "10"
	if s, err := strconv.Atoi(v); err == nil {
		fmt.Printf("%T, %v", s, s)
	}

}
// int, 10
```


---




## Alias types

Alias types were introduced in Go 1.9. They allow developers to provide an alternate name for an existing type and use it interchangeably with the underlying type.

```go
package main

import "fmt"

type MyAlias = string

func main() {
	var str MyAlias = "I am an alias"

	fmt.Printf("%T - %s", str, str) // Output: string - I am an alias
}
```

## Defined types

Lastly, we have defined types that unlike alias types do not use an equals sign.

```go
package main

import "fmt"

type MyDefined string

func main() {
	var str MyDefined = "I am defined"

	fmt.Printf("%T - %s", str, str) // Output: main.MyDefined - I am defined
}
```

**But wait...what's the difference?**

So, defined types do more than just give a name to a type.

It first defines a new named type with an underlying type. However, this defined type is different from any other type, including its underline type.

Hence, it cannot be used interchangeably with the underlying type like alias types.

It's a bit confusing at first, hopefully, this example will make things clear.

```go
package main

import "fmt"

type MyAlias = string

type MyDefined string

func main() {
	var alias MyAlias
	var def MyDefined

	// ✅ Works
	var copy1 string = alias

	// ❌ Cannot use def (variable of type MyDefined) as string value in variable
	var copy2 string = def

	fmt.Println(copy1, copy2)
}
```

As we can see, we cannot use the defined type interchangeably with the underlying type, unlike _alias types_.

# String Formatting

In this tutorial, we will learn about string formatting or sometimes also known as templating.

`fmt` package contains lots of functions. So to save time, we will discuss the most frequently used functions. Let's start with `fmt.Print` inside our main function.

```go
...

fmt.Print("What", "is", "your", "name?")
fmt.Print("My", "name", "is", "golang")
...
```

```bash
$ go run main.go
Whatisyourname?Mynameisgolang
```

As we can see, `Print` does not format anything, it simply takes a string and prints it.

Next, we have `Println` which is the same as `Print` but it adds a new line at the end and also inserts space between the arguments.

```go
...

fmt.Println("What", "is", "your", "name?")
fmt.Println("My", "name", "is", "golang")
...
```

```bash
$ go run main.go
What is your name?
My name is golang
```

That's much better!

Next, we have `Printf` also known as _"Print Formatter"_, which allows us to format numbers, strings, booleans, and much more.

Let's look at an example.

```go
...
name := "golang"

fmt.Println("What is your name?")
fmt.Printf("My name is %s", name)
...
```

```bash
$ go run main.go
What is your name?
My name is golang
```

As we can see that `%s` was substituted with our `name` variable.

But the question is what is `%s` and what does it mean?

So, these are called _annotation verbs_ and they tell the function how to format the arguments. We can control things like width, types, and precision with these and there are lots of them. Here's a [cheatsheet](https://pkg.go.dev/fmt).

Now, let's quickly look at some more examples. Here we will try to calculate a percentage and print it to the console.

```go
...
percent := (7.0 / 9) * 100
fmt.Printf("%f", percent)
...
```

```bash
$ go run main.go
77.777778
```

Let's say we want just `77.78` which is 2 points precision, we can do that as well by using `.2f`.

Also, to add an actual percent sign, we will need to _escape it_.

```go
...
percent := (7.0 / 9) * 100
fmt.Printf("%.2f %%", percent)
...
```

```bash
$ go run main.go
77.78 %
```

This brings us to `Sprint`, `Sprintln`, and `Sprintf`. These are basically the same as the print functions, the only difference being they return the string instead of printing it.

Let's take a look at an example.

```go
...
s := fmt.Sprintf("hex:%x bin:%b", 10 ,10)
fmt.Println(s)
...
```

```bash
$ go run main.go
hex:a bin:1010
```

So, as we can see `Sprintf` formats our integer as hex or binary and returns it as a string.

Lastly, we have multiline string literals, which can be used like this.

```go
...
msg := `
Hello from
multiline
`

fmt.Println(msg)
...
```

Great! But this is just the tip of the iceberg...so make sure to check out the go doc for `fmt` package.

For those who are coming from C/C++ background, this should feel natural, but if you're coming from, let's say Python or JavaScript, this might be a little strange at first. But it is very powerful and you'll see this functionality used quite extensively.


# Modules

In this tutorial, we will learn about modules.

## What are modules?

Simply defined, A module is a collection of [Go packages](https://go.dev/ref/spec#Packages) stored in a file tree with a `go.mod` file at its root, provided the directory is _outside_ `$GOPATH/src`.

Go modules were introduced in Go 1.11, which brings native support for versions and modules. Earlier, we needed the `GO111MODULE=on` flag to turn on the modules functionality when it was experimental. But now after Go 1.13 modules mode is the default for all development.

**But wait, what is `GOPATH`?**

Well, `GOPATH` is a variable that defines the root of your workspace and it contains the following folders:

- **src**: contains Go source code organized in a hierarchy.
- **pkg**: contains compiled package code.
- **bin**: contains compiled binaries and executables.

![gopath](https://raw.githubusercontent.com/karanpratapsingh/portfolio/master/public/static/courses/go/chapter-I/modules/gopath.png)

Like earlier, let's create a new module using `go mod init` command which creates a new module and initializes the `go.mod` file that describes it.

```bash
$ go mod init example
```

The important thing to note here is that a Go module can correspond to a Github repository as well if you plan to publish this module. For example:

```bash
$ go mod init example
```

Now, let's explore `go.mod` which is the file that defines the module's _module path_ and also the import path used for the root directory, and its _dependency requirements_.

```go
module <name>

go <version>

require (
	...
)
```

And if we want to add a new dependency, we will use `go install` command:

```bash
$ go install github.com/rs/zerolog
```

As we can see a `go.sum` file was also created. This file contains the expected [hashes](https://go.dev/cmd/go/#hdr-Module_downloading_and_verification) of the content of the new modules.

We can list all the dependencies using `go list` command as follows:

```bash
$ go list -m all
```

If the dependency is not used, we can simply remove it using `go mod tidy` command:

```bash
$ go mod tidy
```

Finishing up our discussion on modules, let's also discuss vendoring.

Vendoring is the act of making your own copy of the 3rd party packages your project is using. Those copies are traditionally placed inside each project and then saved in the project repository.

This can be done through `go mod vendor` command.

So, let's reinstall the removed module using `go mod tidy`.

```go
package main

import "github.com/rs/zerolog/log"

func main() {
	log.Info().Msg("Hello")
}
```

```bash
$ go mod tidy
go: finding module for package github.com/rs/zerolog/log
go: found github.com/rs/zerolog/log in github.com/rs/zerolog v1.26.1
```

```bash
$ go mod vendor
```

After the `go mod vendor` command is executed, a `vendor` directory will be created.

```
├── go.mod
├── go.sum
├── go.work
├── main.go
└── vendor
    ├── github.com
    │   └── rs
    │       └── zerolog
    │           └── ...
    └── modules.txt
```

# Packages

In this tutorial, we will talk about packages.

## What are packages?

A package is nothing but a directory containing one or more Go source files, or other Go packages.

This means every Go source file must belong to a package and package declaration is done at top of every source file as follows.

```go
package <package_name>
```

So far, we've done everything inside of `package main`. By convention, executable programs (by that I mean the ones with the `main` package) are called _Commands_, others are simply called _Packages_.

The `main` package should also contain a `main()` function which is a special function that acts as the entry point of an executable program.

Let's take a look at an example by creating our own package `custom` and adding some source files to it such as `code.go`.

```go
package custom
```

Before we proceed any further, we should talk about imports and exports. Just like other languages, go also has a concept of imports and exports but it's very elegant.

Basically, any value (like a variable or function) can be exported and visible from other packages if they have been defined with an upper case identifier.

Let's try an example in our `custom` package.

```go
package custom

var value int = 10 // Will not be exported
var Value int = 20 // Will be exported
```

As we can see lower case identifiers will not be exported and will be private to the package it's defined in. In our case the `custom` package.

That's great but how do we import or access it? Well, same as we've been doing so far unknowingly. Let's go to our `main.go` file and import our `custom` package.

Here we can refer to it using the `module` we had initialized in our `go.mod` file earlier.

```go
---go.mod---
module example

go 1.18

---main.go--
package main

import "example/custom"

func main() {
	custom.Value
}
```

_Notice how the package name is the last name of the import path._

We can import multiple packages as well like this.

```go
package main

import (
	"fmt"

	"example/custom"
)

func main() {
	fmt.Println(custom.Value)
}
```

We can also alias our imports to avoid collisions like this.

```go
package main

import (
	"fmt"

	abcd "example/custom"
)

func main() {
	fmt.Println(abcd.Value)
}
```

## External Dependencies

In Go, we are not only limited to working with local packages, we can also install external packages using `go install` command as we saw earlier.

So let's download a simple logging package `github.com/rs/zerolog/log`.

```bash
$ go install github.com/rs/zerolog
```

```go
package main

import (
	"github.com/rs/zerolog/log"

	abcd "example/custom"
)

func main() {
	log.Print(abcd.Value)
}
```

Also, make sure to check out the go doc of packages you install, which is usually located in the project's readme file. go doc parses the source code and generates documentation in HTML format. Reference to It is usually located in readme files.

Lastly, I will add that, Go doesn't have a particular _"folder structure"_ convention, always try to organize your packages in a simple and intuitive way.

# Workspaces

In this tutorial, we will learn about multi-module workspaces that were introduced in Go 1.18.

Workspaces allow us to work with multiple modules simultaneously without having to edit `go.mod` files for each module. Each module within a workspace is treated as a root module when resolving dependencies.

To understand this better, let's start by creating a `hello` module.

```bash
$ mkdir workspaces && cd workspaces
$ mkdir hello && cd hello
$ go mod init hello
```

For demonstration purposes, I will add a simple `main.go` and install an example package.

```go
package main

import (
	"fmt"

	"golang.org/x/example/stringutil"
)

func main() {
	result := stringutil.Reverse("Hello Workspace")
	fmt.Println(result)
}
```

```bash
$ go get golang.org/x/example
go: downloading golang.org/x/example v0.0.0-20220412213650-2e68773dfca0
go: added golang.org/x/example v0.0.0-20220412213650-2e68773dfca0
```

And if we run this, we should see our output in reverse.

```bash
$ go run main.go
ecapskroW olleH
```

This is great, but what if we want to modify the `stringutil` module that our code depends on?

Until now, we had to do it using the `replace` directive in the `go.mod` file, but now let's see how we can use workspaces here.

So, let's create our workspace in the `workspaces` directory.

```bash
$ go work init
```

This will create a `go.work` file.

```bash
$ cat go.work
go 1.18
```

We will also add our `hello` module to the workspace.

```bash
$ go work use ./hello
```

This should update the `go.work` file with a reference to our `hello` module.

```go
go 1.18

use ./hello
```

Now, let's download and modify the `stringutil` package and update the `Reverse` function implementation.

```bash
$ git clone https://go.googlesource.com/example
Cloning into 'example'...
remote: Total 204 (delta 39), reused 204 (delta 39)
Receiving objects: 100% (204/204), 467.53 KiB | 363.00 KiB/s, done.
Resolving deltas: 100% (39/39), done.
```

`example/stringutil/reverse.go`

```go
func Reverse(s string) string {
	return fmt.Sprintf("I can do whatever!! %s", s)
}
```

Finally, let's add `example` package to our workspace.

```bash
$ go work use ./example
$ cat go.work
go 1.18

use (
	./example
	./hello
)
```

Perfect, now if we run our `hello` module we will notice that the `Reverse` function has been modified.

```bash
$ go run hello
I can do whatever!! Hello Workspace
```

_This is a very underrated feature from Go 1.18 but it is quite useful in certain circumstances._

# Useful Commands

During our module discussion, we discussed some go commands related to go modules, let's now discuss some other important commands.

Starting with `go fmt`, which formats the source code and it's enforced by that language so that we can focus on how our code should work rather than how our code should look.

```bash
$ go fmt
```

This might seem a little weird at first especially if you're coming from a javascript or python background like me but frankly, it's quite nice not to worry about linting rules.

Next, we have `go vet` which reports likely mistakes in our packages.

So, if I go ahead and make a mistake in the syntax, and then run `go vet`.

It should notify me of the errors.

```bash
$ go vet
```

Next, we have `go env` which simply prints all the go environment information, we'll learn about some of these build-time variables later.

Lastly, we have `go doc` which shows documentation for a package or symbol, here's an example of the `fmt` package.

```bash
$ go doc -src fmt Printf
```

Let's use `go help` command to see what other commands are available.

```bash
$ go help
```

As we can see, we have:

`go fix` finds Go programs that use old APIs and rewrites them to use newer ones.

`go generate` is usually used for code generation.

`go install` compiles and installs packages and dependencies.

`go clean` is used for cleaning files that are generated by compilers.

Some other very important commands are `go build` and `go test` but we will learn about them in detail later in the course.

# Build

Building static binaries is one of the best features of Go which enables us to ship our code efficiently.

We can do this very easily using the `go build` command.

```go
package main

import "fmt"

func main() {
	fmt.Println("I am a binary!")
}
```

```bash
$ go build
```

This should produce a binary with the name of our module. For example, here we have `example`.

We can also specify the output.

```bash
$ go build -o app
```

Now to run this, we simply need to execute it.

```bash
$ ./app
I am a binary!
```

_Yes, it's as simple as that!_

Now, let's talk about some important build time variables, starting with:

- `GOOS` and `GOARCH`

These environment variables help us build go programs for different [operating systems](https://en.wikipedia.org/wiki/Operating_system)
and underlying processor [architectures](https://en.wikipedia.org/wiki/Microarchitecture).

We can list all the supported architecture using `go tool` command.

```bash
$ go tool dist list
android/amd64
ios/amd64
js/wasm
linux/amd64
windows/arm64
.
.
.
```

Here's an example for building a windows executable from macOS!

```bash
$ GOOS=windows GOARCH=amd64 go build -o app.exe
```

- `CGO_ENABLED`

This variable allows us to configure [CGO](https://go.dev/blog/cgo), which is a way in Go to call C code.

This helps us to produce a [statically linked binary](https://en.wikipedia.org/wiki/Static_build) that works without any external dependencies.

This is quite helpful for, let's say when we want to run our go binaries in a docker container with minimum external dependencies.

Here's an example of how to use it:

```bash
$ CGO_ENABLED=0 go build -o app
```


# 