# Git

## What is it?

Open source distributed version control system designed for all scales of projects.
Git keeps track the snapshot of your project over time

Git maintains the state of current repository inside .git folder

Git Workspace Areas

| Working                                                                      | Staging                         | Repo                       |
| ---------------------------------------------------------------------------- | ------------------------------- | -------------------------- |
| You are typically working with your editor, all the files are not staged yet | The place for the draft changes | Area for permanent changes |

## Git Objects

Blob : Binary Large Object that refers to collection of your raw data. This is created once you staged changes.

Tree : Keeps references to child blobs or trees

Commit : Something like tree , but also contains commit information like author, committer, message

![git_objects](/assets/git/git-objects.png)

## Git Objects Data Structure

HashTree or Merkle Tree is used as data structure to store Git Objects properly

## Git Storage Cost

Since Git takes a snapshot of your projects with changeset when you commit to repo can we say total size of git storage = # of commit x avg(commit size)?
No Git uses compression algorithm to compress nearly similar files, it is smarter than you think 

### Git Diff

it compares Working Area and Staging Area

> git diff --stage compares Staging Area and Repo Area

### Idea as Git Tool

~/.gitconfig Global or local

you set mergetool or difftool

## Git Reset

> git reset --hard HEAD means clear everything which is not committed yet.

> git reset reverts everything on staging back to working area

## Git Rebase

This command is for rewriting the history

> git rebase -i HEAD-3 is for edit history from current place down to 3 commits back
