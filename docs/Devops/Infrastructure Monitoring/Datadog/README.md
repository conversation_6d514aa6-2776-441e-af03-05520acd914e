# Datadog (https://docs.datadoghq.com/getting_started/)

Data dog is an observability platform for cloud-scale applications, providing monitoring of servers, databases , tools and services , through a SaaS-based data analytics platform

## Monitoring

Monitoring is the process of gathering data to understand what's going on inside of your infrastructure

## Observability

Observability is taking the same data that you have collected and moving beyond "What is happening? and Why is it happenning?"

### Three Pillars of Observability

> Metrics ,Logs ,Traces

## What is a Metrics?

Metrics are numerical values that can track anything about your environment over time, from latecy to error rates to user sigups. These data points are numerical values that can track anything about your environment over time

### Reasons to collect Metrics

> Baseline for Operations => Metrics can tell us what normal looks like for our applications. Without metrics, we're stuck guessing what's going on.
> Reactive Responses => Using metrics we don't have to wait until a customer reports an outage. We can react to issues in our environment before they snowball
> Proactive Responses => Why wait for something to go wrong? By looking at metrics we can get ahead of problems before they happen

## What is a log?

A computer generated file that contains time stamped information about the usage of that system . A log is a computer generated file that contains information regarding the usage of a system. This gives you insight into the behavior of the resource

### Why do we collect Logs?

> Compliance => Standards that the business is held to might dictate which logs you'll need to store and for how long you need to store them 
> Insight => Logs can give you insight into application and system performance that metrics by themselves might not be able to provide
> Security => This is a priority for businesses. Logs are needed to demonstrate that only authorized activities are going on inside of a system.

## What is a Trace?

Used to track the time spent by an application processing a request and the status of this request.

## What is a host?

A host is any physical or virtual OS instance that you monitor with Datadog
It could be a server, VM, node(in the case of Kubernates) or App service Plan instance(in the case of Azure App Services). In simple words this is your server where Datadog agent is installed.

## What is a Datadog Agent?

The Datadog Agent is software that runs on your hosts. It collects events and metrics from hosts and sends them to Datadog where you can analyze your monitoring and performence data Datadog Agent is the heart of Datadog
The Datadog Agent acts like a middle layer between your application and Datadog website.
The Datadog Agent collects metric and events from your systems and apps.

## What is a Datadog tags?

Tags are a way of adding dimensions to datadog telemeries so they can be filtered, aggregated, and compared in Datadog visualizations. Using tags enables you to observe aggregate performance across several hosts. Example : version: stage, version:prod


    Datadog is a software-as-a-service (SaaS) observability platform that is designed to:

        * Collect the vast amounts of monitoring data—including metrics, traces, and logs—that your systems produce
        * Put all that data in one place
        * Organize and present the data in an intuitive format that helps you and your team detect, diagnose, and resolve issues more effectively 


## Universal Service Monitoring (USM)

Not many years ago, a web service might exist on a single machine. Today, a service such as store-frontend might be deployed across hundreds of compute instances. But you still need to look at that service’s activity in aggregate across all instances. That’s where Universal Service Monitoring comes in.

Universal Service Monitoring (USM) offers a view of service health metrics across your entire technology stack, without the need to instrument your code. Instead, it relies on the presence of a configured Datadog Agent and Unified Service Tagging to collect data about your existing services, which can then be viewed through the Service Catalog.


    Enabling USM requires the following:

        * If on Linux, your service must be running in a container.
        * If on Windows and using IIS, your service must be running on a virtual machine.
        * The Datadog Agent needs to be installed alongside your service.
        * The env tag for Unified Service Tagging must be applied to your deployment.

## Service Catalog

The Service Catalog is a centralized place to view all services in your application. Service Catalog automatically includes services detected by USM. Developers and site reliability engineers can view all services, their structures, and links to more information. It improves the on-call experience for everyone by establishing correct ownership information and communication channels, alongside easy access to monitoring and troubleshooting details. In case of an incident, it can speed up recovery by increasing confidence and making it simpler to locate owners of upstream and downstream services and dependencies

Service Catalog is a centralized place to access important information about all services in your organization. It displays service metrics and other information based on Application Performance Monitoring (APM) traces, Real User Monitoring (RUM) events, and Universal Service Monitoring (USM) telemetry.


## Datadog Log Management

Allows you to cost-effectively collect, process, *archive, explore, and monitor all your logs without limitations.

    When you use Datadog Log Management:

    * You can collect logs from various sources, such as hosts, containers, and cloud providers.
    * Once the logs are ingested, you can enhance them using pipelines and processors, create metrics from the logs, and manage storage-optimized archives with Log Configuration options.
    * You can connect logs to metrics and traces from other sources for greater insights.
    * Finally, you can search, filter, and query the ingested logs in the Log Explorer.