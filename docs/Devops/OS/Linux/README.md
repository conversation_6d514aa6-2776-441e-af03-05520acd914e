# Linux

What is Linux?

* BIOS
Basic Input/Output system
It's a special type of firmware used in the booting process
It is operating system independent.PRimary purpose is to find and execute the boot loader. It's primary purpose is to test the underlying hardware components and to load a boot loader or operating system
The BIOS perform perform a POST which stands for "power on self test"
The POST performs some basic checks of various hardware components (DVD,USB vs) such as the CPU, memory, and storage devices. Only if the POST succeeds will the BIOS attempt to load the boot loader.

* The Boot Loader

Once a bootable device has been found, the BIOS will run the boot loader. 
Typically, the GRUB bootloader will be used, but you may run into some older Linux systems that still use the LILO bootloader. 
LILO stands for "Linux loader" while GRUB stands for "Grand Unified Bootloader". 
In any case, the primary purpose of the bootloader is to start the operating system.

* The initial RAM disk
initrd
The initial RAM disk, abbreviated "initrd" is a temporary filesystem that is loaded into memory when the system boots.
Contains helpers and modules required to load the permanent OS file system
For example, if the root filesystem is stored on a LVM volume, or "logical volume manager" volume the initrd image will contain the kernel modules required to mount that logical volume as the root filesystem. Once the initrd mounts the actual root filesystem, it's job is done and the operating system continues loading from the real root filesystem.

The initial RAM disk, or initrd is a temporary files system that is loaded into memory. It's main job is to mount the filesystem where the operating system is stored.


The Linux kernel, the initial RAM disk and other files needed to boot the operating system are stored in /boot Here's a listing of the /boot directory for an Ubuntu system.

/boot
    Contains the files required to boot Lİnux
    initrd
    kernel
    bootloader configuration


![boot directory](/assets/Linux/linux_boot_directory.png "qwe")

- The kernel is typically named "vmlinux" or "vmlinuz". If the kernel is compressed its name ends in 'z' In this example, the kernel is "vmlinuz-3.13.0-46-generic" The initial RAM disk in this example is "initrd.img-3.13.0-46-generic"

* The Linux Kernel
* Kernel Ring Buffer

- Kernel ring buffer contains messages related the Linux kernel A ring buffer is a data structure that is always the same size Once the buffer is completely full old messages are discarded when new messages arrive

- dmesg ( To see the contents of the kernel ring buffer, use the dmesg command, spelled "dmesg" On most Linux distributions these messages are also stored on disk in the /var/log/dmesg file Between the dmesg command and the /var/log/dmesg log file you will be able to see the messages the kernel is generating, even during the earliest stages of the boot process, when those messages can quickly fly by your screen on boot up.)


* Runlevels / Description

Linux uses runlevels to determine

what processes and services to start.

Each distribution can be configured differently

but in general, 
runlevel 0 is used to power off a system
runlevel 1 is single user mode .Used for maintenance
runlevels 2 Multi*user mode with grapgical interface (debian/Ubuntu ) 
runlevels 3 Multi-user text mode (RedHat/Centos) 
runlevels 4 Undefined
runlevels 5 Multi*user mode with grapgical interface (redhat/Centos ) 
runlevel 6 is used to reboot a system
Traditionally, runlevels were controlled by the init program.

-Telinit(init) 
You learned to change the runlevel with the "telinit" command for the traditional init system and the "systemctl" (systemd) command for the systemd system.

/etc/inittab runlevels configuration

shutdown reboot poweroff

# System Logging
Linux uses the syslog standard for message logging. This allows programs and applications to generate messages that can be captured, processed, and stored by a system logger. It eliminates the need for each and every application having to implement a logging mechanism. It also means that logging can be configured and controlled in a central location. The syslog standard uses facilities and severities to categorize messages. Each message is labeled with a facility code and a severity level.

A syslog server accepts syslog messages and processes those messages based on a set of configurable rules. Traditionally the syslogd daemon filled this role but many Linux distributions ship with alternatives such as 'rsyslog' and 'syslog-ng'.

The main configuration file for rsyslog is /etc/rsyslog.conf. You can include additional configuration files by using the '$IncludeConfig' directive.

- Logging Rules 
Logiging rules consist of two fields

First field "Selector field"
                FACILITY.SEVERTY
                mail.*
                mail
                FACILITY.none
                FACILITY_1.SEVERTY;FACILITY_2.SERVERTY
Second field "Action field"
                Determines how a message is processed   

-Generate system log
logger         [options] message
Options:
    -p FACILITY.SEVERITY
    -t TAG        

-logrotate
You can use the 'logrotate' tool to rotate, compress, remove, and even mail log files.

This provides an automated way to manage log files and can help prevent filling up your storage space with log messages.

The configuration file for logrotate is located at /etc/logrotate.conf.    



# Disk Management