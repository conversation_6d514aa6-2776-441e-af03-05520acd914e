# Doc Tree

.
├── root
│   ├── Version Control Systems
│   │   │── Git
│   │   │── Services 
│   │   │   ├── GitHub.md
│   │   │   └── GitLab.md
│   ├── Design and Development Principles
│   │   │── Principles
│   │   │   ├── [SOLID](/docs/DesignAndDevelopmentPrinciples/Principles/solid.md)
│   │   │   ├── [IoC](/docs/DesignAndDevelopmentPrinciples/Principles/inversion-of-control.md)
│   │   │   ├── DDD
│   │   │   ├── TDD
│   │   │   └── Gang of Four Design Patterns
│   │   │── Memory Management Techniques
│   │   │── Software-Architecture-Pattern
│   │   │   ├── Messaging & Event-Driven Architecture
│   │   │   │   ├── Message Brokers (RabbitMQ, NATS.io, SQS)
│   │   │   │   │   ├── [RabbitMQ](/docs/DesignAndDevelopmentPrinciples/SoftwareArchitecturel/Software-Architecture-Pattern/Messaging-Event-Driven-Architecture/Message-Brokers/RabbitMQ/readme.md)
│   │   │   │   ├── Event Streaming (Kafka, Pulsar, Redpanda)
│   │   │   │   │   ├── [Kafka](/docs/DesignAndDevelopmentPrinciples/SoftwareArchitecturel/Software-Architecture-Pattern/Messaging-Event-Driven-Architecture/Event-Streaming/Kafka/readme.md)
│   │   │   │   ├── Message Bus & Abstractions (MassTransit, CAP, Rebus)
│   │   │   │   └── Patterns & Concepts (Pub/Sub, Event Sourcing, Saga)
│   │   │   ├── Microservices
│   │   │   ├── CQRS
│   │   │   ├── Event Sourcing
│   │   │   └── Monolith vs Microservices
│   │   │── Software-Design-Pattern
│   │   ├── API Design
│   │   │   ├── RESTful Principles
│   │   │   ├── gRPC Design
│   │   │   ├── WebSocket Communication
│   │   │   └── SOAP Best Practices
│   │   └── Testing
│   ├── Programming-Languages
│   │   ├── Javascript
│   │   │   └── Fundamentals
│   │   │   ├── Frameworks & Libraries
│   │   │   │   ├── Vue.js
│   │   │   │   ├── React
│   │   │   │   └── Redux-Saga, Immutable.js, Reselect, normalizr
│   │   │   ├── Build Tools
│   │   │   │   │   ├── Task Runners
│   │   │   │   │   │   └── NPM Scripts
│   │   │   │   │   ├── Linters formatters
│   │   │   │   │   │   ├── ESLint
│   │   │   │   │   │   └── Prettier
│   │   │   │   │   └── Module Bundlers
│   │   │   │   │   │   └── Webpack
│   │   ├── Typescript
│   │   │   └── Fundamentals
│   │   ├── C#
│   │   │   ├── [Fundamentals](/docs/languages/csharp/Fundamentals/readme.md)
│   │   │   ├── Runtime & Memory
│   │   │   │   └── Garbage Collection
│   │   │   ├── Frameworks & Libraries
│   │   │   │   ├── [DotNet Core](/docs/languages/csharp/FrameworksAndLibraries/DotNet-Core/readme.md)
│   │   │   │   ├── Entity Framework Core
│   │   │   │   ├── Dapper
│   │   │   │   ├── Serilog
│   │   │   │   ├── MediatR
│   │   │   │   ├── FluentValidation
│   │   │   │   ├── Polly
│   │   │   │   ├── Akka.net
│   │   │   │   ├── Hangfire
│   │   │   │   └── Quartz
│   │   ├── Golang
│   │   │   └── Fundamentals
│   │   ├── Kotlin
│   │   │   └── Fundamentals
│   │   ├── C/C++
│   │   │   └── Fundamentals
│   ├── DBA
│   │   ├── [Fundamentals](/docs/DBA/fundamentals/readme.md)
│   │   ├── Databases
│   │   │   ├── Relational
│   │   │   │    ├── MSSQL
│   │   │   │    └── PostgreSQL
│   │   │   ├── Cloud Databases
│   │   │   │    └── Amazon DynamoDB
│   │   │   ├── Search Engines
│   │   │   │    └── ElasticSearch
│   │   │   ├── NoSQL
│   │   │   │    ├── Redis
│   │   │   │    └── MongoDB
│   ├── OS Fundamentals
│   │   ├── Terminal Usage
│   │   ├── How OSs work General
│   │   ├── Process Management
│   │   ├── Memory Management
│   │   ├── Interprocess Communication
│   │   ├── I/O management
│   │   ├── Posix basics (stdin,stdout,stderr,pipes)
│   │   ├── Basic networking concepts
│   │   ├── File System
│   │   ├── Virtualization
│   │   ├── Startup(intid) / Service(systemd) management
│   │   ├── Threads and Concurrency
│   │   ├── sockets
│   │   ├── OS
│   │   │   └── Linux / Ubuntu / CentOS
│   ├── Infrastructure & Networking
│   │   ├── Fundamentals
│   │   │   ├── What is the internet and how it works
│   │   │   ├── Progressive Web Apps
│   │   │   └── Browsers and how they work
│   │   ├── HTTP / HTTPS / FTP / SSL / TLS / SSH / Port Forwarding 
│   │   ├── Security  
│   │   │   ├── Cors / Content Security Policy / OWASP Security Risks
│   │   │   ├── SSL/TLS
│   │   │   ├── Hashing Algorithms
│   │   │   └── Encryption algorithms
│   │   ├── DNS / Domain Name / Hosting
│   │   ├── Caching Strategies
│   │   │   ├── CDN (Content Delivery Network)
│   │   │   ├── Server side
│   │   │   │    ├── Memory Cache
│   │   │   │    └── Distributed Cache  
│   │   │   └── Client side
│   │   ├── Infrastructure Monitoring
│   │   │   ├── Grafana
│   │   │   ├── Application Insights Distributed Tracing
│   │   │   └── Datadog
│   │   ├── Web Servers
│   │   │   ├── Nginx
│   │   │   ├── ISS
│   │   │   └── Apache
│   │   ├── Containerization / Virtualization / Infrastructure Provisioning
│   │   │   ├── Dockers
│   │   │   ├── Kubernetes
│   │   │   └── Terraform
│   │   ├── Automation
│   │   │   ├── helm.md
│   │   │   └── ansible.md
│   ├── CI/CD Tool
│   │   ├── Jenkins
│   │   ├── Argo
│   │   ├── Drone
│   │   ├── Github Actions
│   │   └── Azure devops
│   ├── Cloud Providers
│   │   ├── AWS
│   │   └── Google Cloud
